const { EmbedBuilder, MessageFlags } = require('discord.js');

module.exports = async (interaction, client) => {
    if (interaction.customId !== 'feedbackModal') return;

    const feedback = interaction.fields.getTextInputValue('feedbackInput');
    const feedbackUser = interaction.user;

    const feedbackEmbed = new EmbedBuilder()
        .setTitle('New Feedback')
        .setDescription(feedback)
        .setFooter({ text: `Feedback from ${feedbackUser.tag}`, iconURL: feedbackUser.displayAvatarURL() })
        .setColor('Blurple');
        
    const feedbackChannel = client.channels.cache.get('1386070679337697280');
    if (!feedbackChannel) return;
    feedbackChannel.send({ embeds: [feedbackEmbed] });

    interaction.reply({ content: 'Feedback sent!', flags: MessageFlags.Ephemeral });
};