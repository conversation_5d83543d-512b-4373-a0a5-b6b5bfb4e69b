const { Modal<PERSON><PERSON>er, TextInputBuilder, TextInputStyle, ActionRowBuilder, MessageFlags } = require('discord.js');

module.exports = {
    data: {
        name: 'feedback',
        description: 'Send feedback to the bot developers',
    },
 
    run: async ({ interaction, client, handler }) => {
        const modal = new ModalBuilder()
            .setCustomId('feedbackModal')
            .setTitle('Send Feedback')

        const feedbackInput = new TextInputBuilder()
            .setCustomId('feedbackInput')
            .setLabel('Feedback')
            .setPlaceholder(`Enter suggestions/bugs/feedback here...`)
            .setStyle(TextInputStyle.Paragraph)
            .setRequired(true)

        const feedbackInputRow = new ActionRowBuilder().addComponents(feedbackInput);
        modal.addComponents(feedbackInputRow);

        await interaction.showModal(modal);
    },
 
    options: {
        deleted: false,
    },
};