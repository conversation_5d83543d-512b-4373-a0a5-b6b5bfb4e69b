const { ButtonKit } = require('commandkit');
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ChannelType, PermissionsBitField } = require('discord.js');

module.exports = async (guild, client) => {
    await new Promise(resolve => setTimeout(resolve, 3000));

    const systemChannel = await guild.systemChannel;

    const joinEmbed = new EmbedBuilder()
        .setTitle('Thanks for adding me to your server!')
        .setDescription(`To setup the stock updates and pings, run </config:1384564115120586812>.\n\nIf you need help, have a suggestion, or found a bug, join [our support server](https://discord.gg/K6FArFC25k) or use </feedback:1386070794022551714>.\n\nLike our bot? Please vote and leave a review on [top.gg](https://top.gg/bot/1383147672374087710)!\n\n[Need social pings?](<https://disping.xyz/>)`)
        .setFooter({ text: `Please don't kick me 🥺` })
        .setThumbnail('https://cdn.discordapp.com/attachments/1378380195408646226/1386072709783486584/garden_notifier-removebg-preview.png?ex=6858604d&is=68570ecd&hm=a21d719c6f01efbabc5d460a552b84a136185a540f93ff71547f5c94bd5682c4&')
        .setColor('#f1fbd2');

    const helpButton = new ButtonKit()
        .setLabel('Need help?')
        .setURL('https://discord.gg/K6FArFC25k')
        .setEmoji('<:external_link:1386318367434084447>')
        .setStyle(ButtonStyle.Link);

    const inviteButton = new ButtonKit()
        .setLabel('Invite me')
        .setURL('https://discord.com/oauth2/authorize?client_id=1383147672374087710')
        .setEmoji('<:Invite:1386318461990469662>')
        .setStyle(ButtonStyle.Link);

    const topggButton = new ButtonKit()
        .setLabel('Top.gg')
        .setURL('https://top.gg/bot/1383147672374087710')
        .setEmoji('<:top_gg:1387824501848412220>')
        .setStyle(ButtonStyle.Link);
    
    const actionRow = new ActionRowBuilder().addComponents(topggButton, helpButton, inviteButton);

    const findFirstChannelWithSendPermission = (guild) => {
        return guild.channels.cache.find(channel => 
            channel.type === ChannelType.GuildText && 
            channel.permissionsFor(guild.members.me).has(PermissionsBitField.Flags.SendMessages)
        );
    };

    if (!systemChannel) {
        const firstChannel = findFirstChannelWithSendPermission(guild);

        if (!firstChannel) return;

        try {
            await firstChannel.send({ embeds: [joinEmbed], components: [actionRow] });
        } catch (error) {
            return;
        }
    } else {
        try {
            await systemChannel.send({ embeds: [joinEmbed], components: [actionRow] });
        } catch (error) {
            const firstChannel = findFirstChannelWithSendPermission(guild);

            if (!firstChannel) return;

            try {
                await firstChannel.send({ embeds: [joinEmbed], components: [actionRow] });
            } catch (error) {
                const logChannel = client.channels.cache.get('1384218793596813456');

                console.log(`Couldn't send welcome message to ${guild.name} (${guild.id})`);
                await logChannel.send(`<:cross:1386075909223747736> Couldn't send welcome message to ${guild.name} (${guild.id})`);
                return;
            }
        }
    }
};