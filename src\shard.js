const { AutoPoster } = require('topgg-autoposter');
const { ShardingManager } = require('discord.js');
require('dotenv').config();

const manager = new ShardingManager('./src/index.js', {
    token: process.env.DISCORD_TOKEN,
    totalShards: 'auto',
});

const poster = AutoPoster(process.env.TOPGG_TOKEN, manager);

poster.on('error', async (err) => {
    console.error(`Error posting stats to Top.gg: ${err}`);
});

manager.on('shardCreate', (shard) => {
    console.log(`🧩 Launched shard ${shard.id}`);
});

manager.spawn();