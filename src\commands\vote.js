const { ApplicationCommandOptionType, EmbedBuilder, MessageFlags, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const os = require('os');

module.exports = {
    data: {
        name: 'vote',
        description: 'Vote for the bot on top.gg'
    },
 
    run: async ({ interaction, client, handler }) => {
        const voteEmbed = new EmbedBuilder()
            .setTitle("🌱 Vote for Garden Notifier")
            .setDescription("Support Garden Notifier by voting on top.gg! Your vote helps us grow and reach more gardeners.\n\n**Why vote?**\n• Help us reach more users\n• Support the development\n• Stay updated with new features\n\n**Current Status:**\n• Bot Uptime: " + Math.floor(process.uptime() / 3600) + " hours\n• Servers: " + client.guilds.cache.size + "\n• Users: " + client.users.cache.size)
            .setColor('#f1fbd2')
            .setThumbnail('https://i.imgur.com/xPnNwfi.png')
            .setFooter({ text: 'Thank you for your support! 🌿', iconURL: client.user.displayAvatarURL() })
            .setTimestamp();

        const voteButton = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setLabel('Vote on top.gg')
                    .setStyle(ButtonStyle.Link)
                    .setURL('https://top.gg/bot/1383147672374087710')
                    .setEmoji('<:top_gg:1387824501848412220>'),
                new ButtonBuilder()
                    .setLabel('Support Server')
                    .setStyle(ButtonStyle.Link)
                    .setURL('https://discord.gg/K6FArFC25k')
                    .setEmoji('<:Invite:1386318461990469662>')
            );

        await interaction.reply({
            embeds: [voteEmbed],
            components: [voteButton]
        });
    },
 
    options: {
        deleted: false,
    },
};