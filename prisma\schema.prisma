// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model StockNotification {
  id                  String   @id @default(cuid())
  guildId             String   @unique
  webhookUrl          String?
  eggWebhookUrl       String?
  weatherWebhookUrl   String?
  eventWebhookUrl     String?
  cosmeticWebhookUrl  String?
  merchantWebhookUrl  String?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  @@map("stock_notifications")
}

model AutoReactionConfig {
  id                      String   @id @default(cuid())
  guildId                 String   @unique
  seedsGearAutoReaction   Boolean  @default(false)
  eggAutoReaction         Boolean  @default(false)
  eventAutoReaction       Boolean  @default(false)
  cosmeticAutoReaction    Boolean  @default(false)
  merchantAutoReaction    Boolean  @default(false)
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt

  @@map("auto_reaction_configs")
}

model RoleConfig {
  id            String        @id @default(cuid())
  guildId       String        @unique
  cosmeticRole  String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Related role mappings
  roleMappings  RoleMapping[]

  @@map("role_configs")
}

model RoleMapping {
  id           String          @id @default(cuid())
  roleConfigId String
  itemName     String
  roleId       String
  type         RoleMappingType
  createdAt    DateTime        @default(now())

  roleConfig   RoleConfig @relation(fields: [roleConfigId], references: [id], onDelete: Cascade)

  @@unique([roleConfigId, itemName, type])
  @@map("role_mappings")
}

enum RoleMappingType {
  SEED
  GEAR
  EGG
  WEATHER
  EVENT
  MERCHANT
}