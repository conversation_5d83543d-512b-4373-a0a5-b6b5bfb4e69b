module.exports = async (guild, client) => {
    await client.shard.broadcastEval(async (c, { guildName, guildId }) => {
        const channelId = '1386069613472776453';
        const channel = await c.channels.fetch(channelId).catch(() => null);
        if (!channel) return false;

        await channel.send(
            `<:remove:1386074663989416036> Removed from **${guildName}** (${guildId})`
        );
        return true;
    }, {
        context: {
            guildName: guild.name,
            guildId: guild.id
        }
    });
};