const { EmbedBuilder } = require('discord.js');
const { sendWebhookMessage } = require('../webhookManager');
const path = require('path');
const fs = require('fs');

module.exports = async function handleWeather({ data, config, roleConfig }) {
    if (!data.stock || data.stock.length === 0) return;

    // Use webhook for sending
    const webhookUrl = config.weatherWebhookUrl;
    if (!webhookUrl) return;

    // Get the weather data from the first item in the stock array
    const weatherData = data.stock[0];

    const weatherDataPath = path.join(__dirname, '../../data/weatherData.json');
    const weatherDataJSON = JSON.parse(fs.readFileSync(weatherDataPath, 'utf8'));

    const weather = weatherDataJSON.find(w => w.apiName === weatherData.weather);
    if (!weather) {
        console.warn(`Weather data not found for ${weatherData.weather}`);
        return;
    }

    const weatherName = weather.name;
    const effects = weather.effects;

    const embed = new EmbedBuilder()
        .setTitle(`🌦️ Weather Event: ${weatherName}`)
        .setColor('#f1fbd2')
        .setTimestamp()
        .setDescription('A new weather event has started!')
        .addFields([
            { name: 'Duration', value: `${weatherData.duration} seconds`, inline: true },
            { name: 'Start', value: `<t:${weatherData.start_timestamp_unix}:R>`, inline: true },
            { name: 'End', value: `<t:${weatherData.end_timestamp_unix}:R>`, inline: true },
            { name: 'Effects', value: effects.join('\n'), inline: false }
        ])
        .setThumbnail(weatherData.image);

    // Add role ping if configured for this weather type
    let content = '';
    if (roleConfig?.weatherRoles && weatherData.weather) {
        const roleId = roleConfig.weatherRoles.get(weatherData.weather);
        if (roleId) {
            content = `<@&${roleId}>`;
        }
    }

    // Prepare message data
    const messageData = {
        content,
        embeds: [embed]
    };

    try {
        // Send via webhook
        const success = await sendWebhookMessage(webhookUrl, messageData);
        if (!success) {
            console.warn(`⚠️ Failed to send webhook message for guild ${config.guildId}, webhook may be invalid`);
            return;
        }
    } catch (error) {
        console.warn(`⚠️ Failed to send weather notification in guild ${config.guildId}: ${error.message}`);
    }
};