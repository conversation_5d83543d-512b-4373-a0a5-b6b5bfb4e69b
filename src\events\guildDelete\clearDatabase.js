const StockNotification = require('../../models/StockNotification');
const RoleConfig = require('../../models/roleConfig');
const AutoReactionConfig = require('../../models/autoReactionConfig');

module.exports = async (guild, client) => {
    try {
        await StockNotification.findOneAndDelete({ guildId: guild.id });
        await RoleConfig.findOneAndDelete({ guildId: guild.id });
        await AutoReactionConfig.findOneAndDelete({ guildId: guild.id });
        console.log(`Cleared config for guild ${guild.name} (${guild.id})`);
    } catch (error) {
        console.error(`Error clearing config for guild ${guild.name} (${guild.id}):`, error);
    }
};