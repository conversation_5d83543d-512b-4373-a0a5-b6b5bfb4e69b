require('dotenv').config();

const { EmbedBuilder } = require("discord.js");
const path = require('path');
const fs = require('fs');
const DEFAULT_ALL_ITEMS_PATH = process.env.ALL_ITEMS_PATH || path.join(__dirname, '../data/allItems.json');
const FALLBACK_ALL_ITEMS_PATH = path.join(__dirname, '..', 'allItemsAlterable.json');
function getAllItemsPath() {
    try {
        require('fs').accessSync(DEFAULT_ALL_ITEMS_PATH);
        return DEFAULT_ALL_ITEMS_PATH;
    } catch {
        return FALLBACK_ALL_ITEMS_PATH;
    }
}

module.exports = {
    data: {
        name: 'stock',
        description: 'Check the Grow a Garden stock'
    },
 
    run: async ({ interaction, client, handler }) => {
        await interaction.deferReply();

        try {
            
            const response = await fetch('https://alpha-v0-lama.3itx.tech/api/v1/stock', {
                method: 'GET',
                headers: {
                    'x-api-key': process.env.API_TOKEN
                },
            });
            data = await response.json();

            const getItemEmoji = (itemName) => {
                const allItemsPath = getAllItemsPath();
                const rawData = fs.readFileSync(allItemsPath, 'utf-8');
                const allItems = JSON.parse(rawData);

                const item = allItems.find(item => item.name === itemName);
                return item ? item.emoji : '❓';
            };

            // Create formatted lists with emojis
            const seedList = (data.seed_stock || []).map(seed => `${getItemEmoji(seed.name)} **${seed.Stock}x** ${seed.name}`).join('\n');
            const gearList = (data.gear_stock || []).map(gear => `${getItemEmoji(gear.name)} **${gear.Stock}x** ${gear.name}`).join('\n');
            const eggList = (data.egg_stock || []).map(egg => `${getItemEmoji(egg.name)} **${egg.Stock}x** ${egg.name}`).join('\n');
            const eventList = (data.eventshop_stock || []).map(event => `${getItemEmoji(event.name)} **${event.Stock}x** ${event.name}`).join('\n');
            const cosmeticList = (data.cosmetic_stock || []).map(cosmetic => `${getItemEmoji(cosmetic.name)} **${cosmetic.Stock}x** ${cosmetic.name}`).join('\n');

            const embed = new EmbedBuilder()
                .setColor('#f1fbd2')
                .setAuthor({ name: 'Grow a Garden Stock', iconURL: 'https://static.wikia.nocookie.net/growagarden/images/e/e6/Site-logo.png/revision/latest?cb=20250524234344' })
                .setFields([
                    {
                        name: '🌱 SEED STOCK',
                        value: seedList || 'No seeds in stock',
                        inline: true
                    },
                    {
                        name: '⚙️ GEAR STOCK',
                        value: gearList || 'No gear in stock',
                        inline: true
                    },
                    {
                        name: '🥚 EGG STOCK',
                        value: eggList || 'No eggs in stock',
                        inline: true
                    },
                    {
                        name: '🎉 EVENT STOCK',
                        value: eventList || 'No event items in stock',
                        inline: true
                    },
                    {
                        name: '🎨 COSMETICS STOCK',
                        value: cosmeticList || 'No cosmetics in stock',
                        inline: true
                    }
                ])
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });
        } catch (error) {
            console.error('Error fetching stock data:', error);
            await interaction.editReply({ content: 'Sorry, there was an error fetching the stock data.' });
        }
    },
 
    options: {
        deleted: false,
    },
};