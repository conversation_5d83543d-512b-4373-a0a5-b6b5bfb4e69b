require('dotenv').config();

const { Client, IntentsBitField, Partials } = require('discord.js');
const { CommandKit } = require('commandkit');
const { AutoPoster } = require('topgg-autoposter');
const path = require('path');

const client = new Client({
    intents: [
        IntentsBitField.Flags.Guilds,
        IntentsBitField.Flags.GuildMessages
    ],
    partials: [
        Partials.Channel,
        Partials.Reaction
    ]
});

new CommandKit({
    client,
    commandsPath: path.join(__dirname, 'commands'),
    eventsPath: path.join(__dirname, 'events'),
    skipBuiltInValidations: true,
    bulkRegister: true,
});

process.on("unhandledRejection", (reason, promise) => {
    console.log("Unhandled Rejection Error");
    console.log(reason, promise)
});

process.on("uncaughtException", (err, origin) => {
    console.log("Uncaught Exeception Error");
    console.log(err, origin);
});

client.login(process.env.DISCORD_TOKEN);