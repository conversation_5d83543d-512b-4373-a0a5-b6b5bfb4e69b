const { EmbedBuilder } = require('discord.js');
const { sendWebhookMessage } = require('../webhookManager');
const path = require('path');
const fs = require('fs');

// Helper function to get allItems.json path
const DEFAULT_ALL_ITEMS_PATH = process.env.ALL_ITEMS_PATH || path.join(__dirname, '../../data/allItems.json');
const FALLBACK_ALL_ITEMS_PATH = path.join(__dirname, '../../allItemsAlterable.json');

function getAllItemsPath() {
    try {
        fs.accessSync(DEFAULT_ALL_ITEMS_PATH);
        return DEFAULT_ALL_ITEMS_PATH;
    } catch {
        return FALLBACK_ALL_ITEMS_PATH;
    }
}

// Helper function to add emoji data to items
function addEmojiData(stockData) {
    try {
        const allItemsPath = getAllItemsPath();
        const allItems = JSON.parse(fs.readFileSync(allItemsPath, 'utf8'));

        return stockData.map(item => {
            const itemData = allItems.find(allItem => allItem.name === item.name);
            return {
                ...item,
                emoji: itemData?.emoji || '❓' // Fallback emoji for events
            };
        });
    } catch (error) {
        console.warn('⚠️ Failed to load emoji data:', error.message);
        return stockData.map(item => ({ ...item, emoji: '🎉' }));
    }
}

module.exports = async function handleEventStock({ data, config, roleConfig, autoReactionConfig }) {
    // Use webhook for sending
    const webhookUrl = config.eventWebhookUrl;
    if (!webhookUrl) return;

    // Add emoji data to stock items
    const stockWithEmojis = addEmojiData(data.stock);

    const embed = new EmbedBuilder()
        .setTitle('🎉 Event Shop Stock Update')
        .setColor('#f1fbd2')
        .setTimestamp()
        .setDescription(stockWithEmojis.map(item => `${item.emoji} **${item.Stock || 0}x** ${item.name}`).join('\n'));

    // Collect role pings for items in stock
    const rolePings = [];
    if (roleConfig?.eventRoles && data.stock) {
        for (const item of data.stock) {
            if (item.Stock > 0 && roleConfig.eventRoles.has(item.name)) {
                const roleId = roleConfig.eventRoles.get(item.name);
                if (!rolePings.includes(roleId)) {
                    rolePings.push(roleId);
                }
            }
        }
    }

    // Build content with role pings
    const content = rolePings.length > 0 ? rolePings.map(roleId => `<@&${roleId}>`).join(' ') : '';

    // Prepare message data
    const messageData = {
        content,
        embeds: [embed]
    };

    try {
        // Send via webhook
        const webhookMessage = await sendWebhookMessage(webhookUrl, messageData);
        if (webhookMessage === null) {
            console.warn(`⚠️ Failed to send webhook message for guild ${config.guildId}, webhook may be invalid`);
            return;
        }

        //if (autoReactionConfig?.eventAutoReaction) {
        //    webhookMessage.react('👍');
        //    webhookMessage.react('👎');
        //}
    } catch (error) {
        console.warn(`⚠️ Failed to send event stock notification in guild ${config.guildId}: ${error.message}`);
    }
};
