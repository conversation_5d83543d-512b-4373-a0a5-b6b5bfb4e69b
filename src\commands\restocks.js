require('dotenv').config();

const { MessageFlags, EmbedBuilder } = require("discord.js");

module.exports = {
    data: {
        name: 'restocks',
        description: 'View restock timers for all stocks'
    },
 
    run: async ({ interaction, client, handler }) => {
        await interaction.deferReply();

        try {
            const response = await fetch('https://alpha-v0-lama.3itx.tech/api/v1/restock-timer', {
                method: 'GET',
                headers: {
                    'x-api-key': process.env.API_TOKEN
                },
            });

            if (!response.ok) {
                throw new Error(`API request failed with status ${response.status}`);
            }

            const data = await response.json();

            if (!data.restocks || !Array.isArray(data.restocks)) {
                throw new Error('Invalid API response format');
            }

            // Create a map for easy lookup of restock data by label
            const restockMap = new Map();
            data.restocks.forEach(restock => {
                restockMap.set(restock.label.toLowerCase(), restock);
            });

            // Helper function to get restock info for a stock type
            const getRestockInfo = (stockType) => {
                const restock = restockMap.get(stockType.toLowerCase());
                if (!restock) {
                    return '❌ No data available';
                }

                // Convert timestamp to seconds (Discord expects seconds, not milliseconds)
                const timestampSeconds = Math.floor(restock.nextRestockTimestamp / 1000);

                // Check if the restock is overdue (timestamp is in the past)
                const now = Math.floor(Date.now() / 1000);
                const isOverdue = timestampSeconds < now;

                if (isOverdue) {
                    return [
                        `🔄 **Restocking now!**`,
                        `**Last Restock:** ${restock.timeSinceLastRestock}`
                    ].join('\n');
                }

                return [
                    `**Next Restock:** <t:${timestampSeconds}:R>`,
                    `**Last Restock:** ${restock.timeSinceLastRestock}`
                ].join('\n');
            };

            // Get emoji mappings for each stock type
            const stockEmojis = {
                'seeds': '🌱',
                'gear': '⚙️',
                'egg': '🥚',
                'event': '🎉',
                'cosmetic': '📦',
                'merchant': '💰'
            };

            const embed = new EmbedBuilder()
                .setTitle("Restock Timers")
                .setDescription("Current restock information for all stock types in Grow a Garden.")
                .setColor("#f1fbd2")
                .setTimestamp();

            // Add fields for each stock type
            const stockTypes = ['seeds', 'gear', 'egg', 'cosmetic', 'merchant'];

            stockTypes.forEach(stockType => {
                const emoji = stockEmojis[stockType] || '📦';
                const stockName = stockType.charAt(0).toUpperCase() + stockType.slice(1);
                const restockInfo = getRestockInfo(stockType);

                embed.addFields({
                    name: `${emoji} ${stockName} Stock`,
                    value: restockInfo,
                    inline: true
                });
            });

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Restocks command error:', error);

            const errorMessage = error.message.includes('API request failed')
                ? "Sorry, the restock API is currently unavailable. Please try again later."
                : "Sorry, there was an error fetching the restock data. Please try again later.";

            await interaction.editReply({
                content: errorMessage,
                flags: MessageFlags.Ephemeral
            });
        }
    },
 
    options: {
        deleted: false,
    },
};