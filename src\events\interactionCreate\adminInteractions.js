const {
    SlashCommandBuilder,
    PermissionFlagsBits,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    EmbedBuilder,
    MessageFlags,
    ChannelSelectMenuBuilder,
    RoleSelectMenuBuilder,
    ModalBuilder,
    TextInputBuilder,
    TextInputStyle
} = require('discord.js');
const StockNotification = require('../../models/StockNotification');
const RoleConfig = require('../../models/roleConfig');
const AutoReactionConfig = require('../../models/autoReactionConfig');
const fs = require('fs').promises;
const path = require('path');

module.exports = async (interaction, c, client, handler) => {
    if (!interaction.customId) return;

    if (interaction.customId === "toggle-stock-updates") {
        try {
            const configPath = path.join(__dirname, '..', '..', 'config.json');
            const configData = JSON.parse(await fs.readFile(configPath, 'utf8'));
            configData[0].notificationsEnabled = !configData[0].notificationsEnabled;
            await fs.writeFile(configPath, JSON.stringify(configData, null, 4));
            const status = configData[0].notificationsEnabled ? 'enabled' : 'disabled';
            await interaction.reply({
                content: `Notifications have been ${status}!`,
                flags: MessageFlags.Ephemeral
            });
        } catch (error) {
            console.error('Error toggling notifications:', error);
            await interaction.reply({
                content: 'There was an error toggling notifications. Please try again later.',
                flags: MessageFlags.Ephemeral
            });
        }
    } else if (interaction.customId === "global-message-btn") {
        const modal = new ModalBuilder()
            .setCustomId('globalMessageModal')
            .setTitle('Send Global Message');

        const titleInput = new TextInputBuilder()
            .setCustomId('globalMessageTitle')
            .setLabel('Embed Title')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter the embed title')
            .setRequired(true);

        const descInput = new TextInputBuilder()
            .setCustomId('globalMessageDesc')
            .setLabel('Embed Description')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Enter the embed description')
            .setRequired(true);

        modal.addComponents(
            new ActionRowBuilder().addComponents(titleInput),
            new ActionRowBuilder().addComponents(descInput)
        );

        await interaction.showModal(modal);
    } else if (interaction.customId === "globalMessageModal") {
        const title = interaction.fields.getTextInputValue('globalMessageTitle');
        const description = interaction.fields.getTextInputValue('globalMessageDesc');

        await interaction.deferReply();

        const globalMessage = {
            embeds: [
                new EmbedBuilder()
                    .setColor('#f1fbd2')
                    .setTitle(title)
                    .setDescription(description)
                    .setTimestamp()
            ]
        };

        // Batch sending system
        const batchSize = process.env.BATCH_SIZE ? parseInt(process.env.BATCH_SIZE) : 10;
        const batchDelay = process.env.BATCH_DELAY ? parseInt(process.env.BATCH_DELAY) : 1000;
        let sentCount = 0;
        let failedCount = 0;

        try {
            const notifications = await StockNotification.find({});
            const batches = [];
            for (let i = 0; i < notifications.length; i += batchSize) {
                batches.push(notifications.slice(i, i + batchSize));
            }

            for (let i = 0; i < batches.length; i++) {
                const batch = batches[i];
                const results = await Promise.allSettled(batch.map(async (notification) => {
                    const guild = interaction.client.guilds.cache.get(notification.guildId);
                    if (!guild) return false;
                    const channel = guild.channels.cache.get(notification.channelId);
                    if (!channel) return false;
                    const botMember = guild.members.me;
                    const botPermissions = channel.permissionsFor(botMember);
                    if (!botPermissions || !botPermissions.has('SendMessages')) {
                        return false;
                    }
                    try {
                        await channel.send(globalMessage);
                        return true;
                    } catch (err) {
                        return false;
                    }
                }));
                sentCount += results.filter(r => r.status === 'fulfilled' && r.value === true).length;
                failedCount += results.filter(r => r.status !== 'fulfilled' || r.value !== true).length;
                // Delay between batches to respect rate limits
                if (i < batches.length - 1) {
                    await new Promise(res => setTimeout(res, batchDelay));
                }
            }

            await interaction.editReply({
                content: `Global message sent to ${sentCount} servers. Failed in ${failedCount} servers.`,
                flags: MessageFlags.Ephemeral
            });
        } catch (error) {
            console.error('Error sending global message:', error);
            await interaction.editReply({
                content: 'There was an error sending the global message. Please try again later.',
                flags: MessageFlags.Ephemeral
            });
        }
    } else if (interaction.customId === "toggle-backup-api") {
        try {
            const configPath = path.join(__dirname, '..', '..', 'config.json');
            const configData = JSON.parse(await fs.readFile(configPath, 'utf8'));
            configData[0].useBackupApi = !configData[0].useBackupApi;
            await fs.writeFile(configPath, JSON.stringify(configData, null, 4));
            const status = configData[0].useBackupApi ? 'enabled' : 'disabled';
            await interaction.reply({
                content: `Backup API mode has been ${status}!`,
                flags: MessageFlags.Ephemeral
            });
        } catch (error) {
            console.error('Error toggling backup API:', error);
            await interaction.reply({
                content: 'There was an error toggling backup API mode. Please try again later.',
                flags: MessageFlags.Ephemeral
            });
        }
    } else if (interaction.customId.startsWith('raw-config-')) {
        // Handle raw config data display
        const guildId = interaction.customId.split('-')[2];

        if (!guildId) {
            return interaction.reply({
                content: '❌ Invalid guild ID',
                flags: MessageFlags.Ephemeral
            });
        }

        try {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });

            // Fetch all configurations for this guild
            const [stockNotification, roleConfig, autoReactionConfig] = await Promise.all([
                StockNotification.findOne({ guildId }),
                RoleConfig.findOne({ guildId }),
                AutoReactionConfig.findOne({ guildId })
            ]);

            // Create formatted raw data
            const rawData = {
                guildId,
                stockNotification: stockNotification || null,
                roleConfig: roleConfig ? {
                    ...roleConfig,
                    // Convert Maps to objects for better display
                    seedRoles: roleConfig.seedRoles ? Object.fromEntries(roleConfig.seedRoles) : {},
                    gearRoles: roleConfig.gearRoles ? Object.fromEntries(roleConfig.gearRoles) : {},
                    eggRoles: roleConfig.eggRoles ? Object.fromEntries(roleConfig.eggRoles) : {},
                    weatherRoles: roleConfig.weatherRoles ? Object.fromEntries(roleConfig.weatherRoles) : {},
                    eventRoles: roleConfig.eventRoles ? Object.fromEntries(roleConfig.eventRoles) : {},
                    merchantRoles: roleConfig.merchantRoles ? Object.fromEntries(roleConfig.merchantRoles) : {}
                } : null,
                autoReactionConfig: autoReactionConfig || null
            };

            // Format as JSON with proper indentation
            const jsonData = JSON.stringify(rawData, null, 2);

            // Split into chunks if too long for Discord message
            const maxLength = 1900; // Leave room for code block formatting
            const chunks = [];

            if (jsonData.length <= maxLength) {
                chunks.push(jsonData);
            } else {
                // Split the JSON data into chunks
                for (let i = 0; i < jsonData.length; i += maxLength) {
                    chunks.push(jsonData.slice(i, i + maxLength));
                }
            }

            // Send the first chunk
            await interaction.editReply({
                content: `\`\`\`json\n${chunks[0]}\n\`\`\``,
            });

            // Send additional chunks as follow-up messages if needed
            for (let i = 1; i < chunks.length; i++) {
                await interaction.followUp({
                    content: `\`\`\`json\n${chunks[i]}\n\`\`\``,
                    flags: MessageFlags.Ephemeral
                });
            }

        } catch (error) {
            console.error('Error fetching raw config data:', error);
            await interaction.editReply({
                content: `❌ An error occurred while fetching raw configuration data: ${error.message}`
            });
        }
    }
};