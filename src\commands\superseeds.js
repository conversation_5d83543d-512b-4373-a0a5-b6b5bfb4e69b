const { EmbedBuilder, MessageFlags } = require('discord.js');

function calculateRobuxToTier(tier) {
    const staticPrices = {
        2: 37, 7: 37, 12: 37,
        17: 99, 22: 99,
        27: 175, 32: 175, 37: 175,
        42: 375, 47: 375, 52: 375, 57: 375, 62: 375, 67: 375
    };

    let totalRobux = 0;
    for (let t = 1; t <= tier; t++) {
        if (t >= 72) {
            if ((t - 72) % 5 === 0) {
                totalRobux += 495;
            }
        } else if (staticPrices[t]) {
            totalRobux += staticPrices[t];
        }
    }

    return totalRobux;
}

module.exports = {
    data: {
        name: 'superseeds',
        description: 'View the price of the top 5 super seeds'
    },
 
    run: async ({ interaction, client, handler }) => {
        await interaction.deferReply();

        try {
            const response = await fetch('https://alpha-v0-lama.3itx.tech/api/v1/foreverpack?find=super&amount=5', {
                method: 'GET',
                headers: {
                    'x-api-key': process.env.API_TOKEN
                },
            });

            if (!response.ok) {
                throw new Error(`API request failed with status ${response.status}`);
            }

            const data = await response.json();

            data.rewards.forEach(reward => {
                if (typeof reward.Tier === 'number') {
                    reward.Price = calculateRobuxToTier(reward.Tier);
                }
            });

            const embed = new EmbedBuilder()
                .setTitle(`Super Seeds - <t:${Math.floor(Date.now() / 1000)}:d>`)
                .setDescription(
                    data.rewards
                        .map((reward, idx) => `**${idx + 1}.** ${reward.Price} <:robux:1397308113308094565>`)
                        .join('\n')
                )
                .setThumbnail('https://i.imgur.com/5PR6pP8.png')
                .setColor('#f1fbd2');

            await interaction.editReply({ embeds: [embed] });
        } catch (error) {
            console.error('Error fetching super seed info:', error);
            await interaction.editReply({ 
                content: 'An error occurred while fetching the super seed data. Please try again later. If the issue persists, join the support server and report the issue.',
                flags: MessageFlags.Ephemeral
            });
        }
    },
 
    options: {
        deleted: false,
    },
};