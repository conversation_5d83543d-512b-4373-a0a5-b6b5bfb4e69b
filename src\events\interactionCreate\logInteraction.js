module.exports = async (interaction) => {
    if (!interaction.isCommand()) return;

    const client = interaction.client;
    const location = interaction.guild ? interaction.guild.name : 'DMs';

    await client.shard.broadcastEval(async (c, { username, userId, commandName, commandId, location }) => {
        const channelId = '1386285256012857345';
        const channel = await c.channels.fetch(channelId).catch(() => null);
        if (!channel) return false;

        await channel.send(`${username} (${userId}) used </${commandName}:${commandId}> in ${location}!`);
        return true;
    }, {
        context: {
            username: interaction.user.username,
            userId: interaction.user.id,
            commandName: interaction.commandName,
            commandId: interaction.commandId,
            location
        }
    });
};