const prisma = require('../../lib/prisma');

class RoleConfig {
    /**
     * Find a role config by guild ID with all role mappings
     * @param {Object} query - Query object
     * @returns {Promise<Object|null>} The role config or null
     */
    static async findOne(query) {
        if (query.guildId) {
            const roleConfig = await prisma.roleConfig.findUnique({
                where: { guildId: query.guildId },
                include: {
                    roleMappings: true
                }
            });

            if (roleConfig) {
                // Convert role mappings back to Map-like structure for compatibility
                return this._convertToMongooseFormat(roleConfig);
            }
        }
        return null;
    }

    /**
     * Find all role configs
     * @returns {Promise<Array>} Array of role configs
     */
    static async find(query = {}) {
        const where = {};
        if (query.guildId) {
            where.guildId = query.guildId;
        }
        
        const roleConfigs = await prisma.roleConfig.findMany({
            where,
            include: {
                roleMappings: true
            }
        });

        return roleConfigs.map(config => this._convertToMongooseFormat(config));
    }

    /**
     * Create or update a role config
     * @param {Object} filter - Filter criteria
     * @param {Object} update - Update data
     * @param {Object} options - Options (upsert, etc.)
     * @returns {Promise<Object>} The created/updated role config
     */
    static async findOneAndUpdate(filter, update, options = {}) {
        const { guildId } = filter;
        
        // Extract role mappings from update
        const { 
            seedRoles, gearRoles, eggRoles, weatherRoles, 
            eventRoles, merchantRoles, cosmeticRole, ...otherUpdates 
        } = update;

        if (options.upsert) {
            // First, upsert the main role config
            const roleConfig = await prisma.roleConfig.upsert({
                where: { guildId },
                update: { cosmeticRole, ...otherUpdates },
                create: { guildId, cosmeticRole, ...otherUpdates }
            });

            // Update role mappings if provided
            await this._updateRoleMappings(roleConfig.id, {
                seedRoles, gearRoles, eggRoles, weatherRoles, eventRoles, merchantRoles
            });

            // Return the updated config
            return await this.findOne({ guildId });
        } else {
            const roleConfig = await prisma.roleConfig.update({
                where: { guildId },
                data: { cosmeticRole, ...otherUpdates }
            });

            // Update role mappings if provided
            await this._updateRoleMappings(roleConfig.id, {
                seedRoles, gearRoles, eggRoles, weatherRoles, eventRoles, merchantRoles
            });

            return await this.findOne({ guildId });
        }
    }

    /**
     * Create a new role config
     * @param {Object} data - The role config data
     * @returns {Promise<Object>} The created role config
     */
    static async create(data) {
        const { 
            guildId, seedRoles, gearRoles, eggRoles, weatherRoles, 
            eventRoles, merchantRoles, cosmeticRole, ...otherData 
        } = data;

        const roleConfig = await prisma.roleConfig.create({
            data: { guildId, cosmeticRole, ...otherData }
        });

        // Create role mappings
        await this._updateRoleMappings(roleConfig.id, {
            seedRoles, gearRoles, eggRoles, weatherRoles, eventRoles, merchantRoles
        });

        return await this.findOne({ guildId });
    }

    /**
     * Delete a role config
     * @param {Object} filter - Filter criteria
     * @returns {Promise<Object|null>} The deleted role config or null if not found
     */
    static async findOneAndDelete(filter) {
        const { guildId } = filter;

        try {
            return await prisma.roleConfig.delete({
                where: { guildId }
            });
        } catch (error) {
            // If record doesn't exist, return null (like Mongoose behavior)
            if (error.code === 'P2025') {
                return null;
            }
            throw error;
        }
    }

    /**
     * Update role mappings for a role config
     * @private
     */
    static async _updateRoleMappings(roleConfigId, mappings) {
        const updates = [];

        for (const [type, roleMap] of Object.entries(mappings)) {
            if (!roleMap) continue;

            const mappingType = type.replace('Roles', '').toUpperCase();

            // Delete existing mappings of this type
            updates.push(
                prisma.roleMapping.deleteMany({
                    where: {
                        roleConfigId,
                        type: mappingType
                    }
                })
            );

            // Create new mappings
            if (roleMap instanceof Map) {
                for (const [itemName, roleId] of roleMap.entries()) {
                    updates.push(
                        prisma.roleMapping.create({
                            data: {
                                roleConfigId,
                                itemName,
                                roleId,
                                type: mappingType
                            }
                        })
                    );
                }
            } else if (typeof roleMap === 'object') {
                for (const [itemName, roleId] of Object.entries(roleMap)) {
                    updates.push(
                        prisma.roleMapping.create({
                            data: {
                                roleConfigId,
                                itemName,
                                roleId,
                                type: mappingType
                            }
                        })
                    );
                }
            }
        }

        if (updates.length > 0) {
            await prisma.$transaction(updates);
        }
    }

    /**
     * Update a single role mapping
     * @private
     */
    static async _updateSingleRoleMapping(roleConfigId, type, itemName, roleId) {
        const mappingType = type.replace('Roles', '').toUpperCase();

        // Use upsert to either update existing or create new mapping
        await prisma.roleMapping.upsert({
            where: {
                roleConfigId_itemName_type: {
                    roleConfigId,
                    itemName,
                    type: mappingType
                }
            },
            update: {
                roleId
            },
            create: {
                roleConfigId,
                itemName,
                roleId,
                type: mappingType
            }
        });
    }

    /**
     * Delete a single role mapping
     * @private
     */
    static async _deleteSingleRoleMapping(roleConfigId, type, itemName) {
        const mappingType = type.replace('Roles', '').toUpperCase();

        try {
            await prisma.roleMapping.delete({
                where: {
                    roleConfigId_itemName_type: {
                        roleConfigId,
                        itemName,
                        type: mappingType
                    }
                }
            });
        } catch (error) {
            // If mapping doesn't exist, that's fine
            if (error.code !== 'P2025') {
                throw error;
            }
        }
    }

    /**
     * Convert Prisma format back to Mongoose-compatible format
     * @private
     */
    static _convertToMongooseFormat(roleConfig) {
        const result = {
            id: roleConfig.id,
            guildId: roleConfig.guildId,
            cosmeticRole: roleConfig.cosmeticRole,
            createdAt: roleConfig.createdAt,
            updatedAt: roleConfig.updatedAt,
            seedRoles: new Map(),
            gearRoles: new Map(),
            eggRoles: new Map(),
            weatherRoles: new Map(),
            eventRoles: new Map(),
            merchantRoles: new Map()
        };

        // Convert role mappings back to Maps
        for (const mapping of roleConfig.roleMappings || []) {
            const mapName = `${mapping.type.toLowerCase()}Roles`;
            if (result[mapName]) {
                result[mapName].set(mapping.itemName, mapping.roleId);
            }
        }

        return result;
    }

    /**
     * Update a single role mapping for a guild
     * @param {string} guildId - The guild ID
     * @param {string} type - The role type (e.g., 'seedRoles', 'gearRoles')
     * @param {string} itemName - The item name
     * @param {string} roleId - The role ID
     * @returns {Promise<Object>} The updated role config
     */
    static async updateSingleRole(guildId, type, itemName, roleId) {
        // First ensure the role config exists
        let roleConfig = await prisma.roleConfig.findUnique({
            where: { guildId }
        });

        if (!roleConfig) {
            roleConfig = await prisma.roleConfig.create({
                data: { guildId }
            });
        }

        // Update the single role mapping
        await this._updateSingleRoleMapping(roleConfig.id, type, itemName, roleId);

        // Return the updated config
        return await this.findOne({ guildId });
    }

    /**
     * Delete a single role mapping for a guild
     * @param {string} guildId - The guild ID
     * @param {string} type - The role type (e.g., 'seedRoles', 'gearRoles')
     * @param {string} itemName - The item name
     * @returns {Promise<Object>} The updated role config
     */
    static async deleteSingleRole(guildId, type, itemName) {
        const roleConfig = await prisma.roleConfig.findUnique({
            where: { guildId }
        });

        if (!roleConfig) {
            return null;
        }

        // Delete the single role mapping
        await this._deleteSingleRoleMapping(roleConfig.id, type, itemName);

        // Return the updated config
        return await this.findOne({ guildId });
    }
}

module.exports = RoleConfig;
