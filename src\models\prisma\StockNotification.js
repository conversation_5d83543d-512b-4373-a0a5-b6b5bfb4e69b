const prisma = require('../../lib/prisma');

class StockNotification {
    /**
     * Find a stock notification by guild ID
     * @param {string} guildId - The guild ID
     * @returns {Promise<Object|null>} The stock notification or null
     */
    static async findOne(query) {
        if (query.guildId) {
            return await prisma.stockNotification.findUnique({
                where: { guildId: query.guildId }
            });
        }
        return null;
    }

    /**
     * Find all stock notifications
     * @returns {Promise<Array>} Array of stock notifications
     */
    static async find(query = {}) {
        const where = {};
        if (query.guildId) {
            where.guildId = query.guildId;
        }
        
        return await prisma.stockNotification.findMany({
            where
        });
    }

    /**
     * Create or update a stock notification
     * @param {Object} filter - Filter criteria
     * @param {Object} update - Update data
     * @param {Object} options - Options (upsert, etc.)
     * @returns {Promise<Object>} The created/updated stock notification
     */
    static async findOneAndUpdate(filter, update, options = {}) {
        const { guildId } = filter;

        // Handle $unset operations (MongoDB style)
        if (update.$unset) {
            return await this.updateWithUnset(filter, update);
        }

        // Handle $set operations (MongoDB style)
        let updateData = update.$set ? { ...update.$set } : { ...update };
        delete updateData.guildId; // Remove guildId from update data

        if (options.upsert) {
            return await prisma.stockNotification.upsert({
                where: { guildId },
                update: updateData,
                create: { guildId, ...updateData }
            });
        } else {
            return await prisma.stockNotification.update({
                where: { guildId },
                data: updateData
            });
        }
    }

    /**
     * Create a new stock notification
     * @param {Object} data - The stock notification data
     * @returns {Promise<Object>} The created stock notification
     */
    static async create(data) {
        return await prisma.stockNotification.create({
            data
        });
    }

    /**
     * Delete a stock notification
     * @param {Object} filter - Filter criteria
     * @returns {Promise<Object|null>} The deleted stock notification or null if not found
     */
    static async findOneAndDelete(filter) {
        const { guildId } = filter;
        try {
            return await prisma.stockNotification.delete({
                where: { guildId }
            });
        } catch (error) {
            // If record doesn't exist, return null (like Mongoose behavior)
            if (error.code === 'P2025') {
                return null;
            }
            throw error;
        }
    }

    /**
     * Update a stock notification with $unset operations
     * @param {Object} filter - Filter criteria
     * @param {Object} update - Update operations
     * @returns {Promise<Object>} The updated stock notification
     */
    static async updateWithUnset(filter, update) {
        const { guildId } = filter;
        const { $unset } = update;
        
        if ($unset) {
            const updateData = {};
            Object.keys($unset).forEach(key => {
                updateData[key] = null;
            });
            
            return await prisma.stockNotification.update({
                where: { guildId },
                data: updateData
            });
        }
        
        return await this.findOneAndUpdate(filter, update);
    }
}

module.exports = StockNotification;
