const { EmbedBuilder } = require('discord.js');
const { sendWebhookMessage } = require('../webhookManager');
const path = require('path');
const fs = require('fs');

// Helper function to get allItems.json path
const DEFAULT_ALL_ITEMS_PATH = process.env.ALL_ITEMS_PATH || path.join(__dirname, '../../data/allItems.json');
const FALLBACK_ALL_ITEMS_PATH = path.join(__dirname, '../../allItemsAlterable.json');

function getAllItemsPath() {
    try {
        fs.accessSync(DEFAULT_ALL_ITEMS_PATH);
        return DEFAULT_ALL_ITEMS_PATH;
    } catch {
        return FALLBACK_ALL_ITEMS_PATH;
    }
}

// Helper function to add emoji data to items
function addEmojiData(stockData) {
    try {
        const allItemsPath = getAllItemsPath();
        const allItems = JSON.parse(fs.readFileSync(allItemsPath, 'utf8'));

        return stockData.map(item => {
            const itemData = allItems.find(allItem => allItem.name === item.name);
            return {
                ...item,
                emoji: itemData?.emoji || '❓' // Fallback emoji for cosmetics
            };
        });
    } catch (error) {
        console.warn('⚠️ Failed to load emoji data:', error.message);
        return stockData.map(item => ({ ...item, emoji: '🎨' }));
    }
}

module.exports = async function handleCosmeticStock({ data, config, roleConfig, autoReactionConfig }) {
    // Use webhook for sending
    const webhookUrl = config.cosmeticWebhookUrl;
    if (!webhookUrl) return;

    // Add emoji data to stock items
    const stockWithEmojis = addEmojiData(data.stock);

    const embed = new EmbedBuilder()
        .setTitle('📦 Cosmetic Stock')
        .setColor('#f1fbd2')
        .setTimestamp()
        .setDescription(stockWithEmojis.map(item => `${item.emoji} **${item.Stock || 0}x** ${item.name}`).join('\n'));

    // Add role ping if configured
    let content = '';
    if (roleConfig?.cosmeticRole) {
        content = `<@&${roleConfig.cosmeticRole}>`;
    }

    // Prepare message data
    const messageData = {
        content,
        embeds: [embed]
    };

    try {
        // Send via webhook
        const webhookMessage = await sendWebhookMessage(webhookUrl, messageData);
        if (webhookMessage === null) {
            console.warn(`⚠️ Failed to send webhook message for guild ${config.guildId}, webhook may be invalid`);
            return;
        }

        //if (autoReactionConfig?.cosmeticAutoReaction) {
        //    webhookMessage.react('👍');
        //    webhookMessage.react('👎');
        //}
    } catch (error) {
        console.warn(`⚠️ Failed to send cosmetic stock notification in guild ${config.guildId}: ${error.message}`);
    }
};