const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder } = require("discord.js");
const StockNotification = require('../../models/StockNotification');
const RoleConfig = require('../../models/roleConfig');
const AutoReactionConfig = require('../../models/autoReactionConfig');

module.exports = async (message, c, client, handler) => {
    if (message.author.id !== '1362805223139049652') return;

    if (message.content.includes("<@1383147672374087710>") && message.content.includes("!serverConfig")) {
        const messageSplit = message.content.split(" ");
        const guildId = messageSplit[2];

        if (!guildId) {
            return message.reply("❌ Please provide a guild ID. Usage: `@bot !serverConfig <guildId>`");
        }

        try {
            // Fetch guild information
            let guild;
            try {
                guild = await client.guilds.fetch(guildId);
            } catch (error) {
                return message.reply(`❌ Could not find guild with ID: \`${guildId}\``);
            }

            // Fetch all configurations for this guild
            const [stockNotification, roleConfig, autoReactionConfig] = await Promise.all([
                StockNotification.findOne({ guildId }),
                RoleConfig.findOne({ guildId }),
                AutoReactionConfig.findOne({ guildId })
            ]);

            // Create the main embed
            const configEmbed = new EmbedBuilder()
                .setTitle(`📊 Server Configuration`)
                .setColor('#f1fbd2')
                .setDescription(`Configuration details for **${guild.name}**`)
                .addFields([
                    {
                        name: '🏠 Server Info',
                        value: `**Name:** ${guild.name}\n**ID:** \`${guild.id}\`\n**Members:** ${guild.memberCount || 'Unknown'}\n**Owner:** <@${guild.ownerId}>`,
                        inline: false
                    }
                ])
                .setThumbnail(guild.iconURL() || null)
                .setTimestamp();

            // Add Stock Notification Configuration
            if (stockNotification) {
                const webhookFields = [];
                if (stockNotification.webhookUrl) webhookFields.push('✅ Seeds/Gears');
                if (stockNotification.eggWebhookUrl) webhookFields.push('✅ Eggs');
                if (stockNotification.weatherWebhookUrl) webhookFields.push('✅ Weather');
                if (stockNotification.eventWebhookUrl) webhookFields.push('✅ Events');
                if (stockNotification.cosmeticWebhookUrl) webhookFields.push('✅ Cosmetics');
                if (stockNotification.merchantWebhookUrl) webhookFields.push('✅ Merchant');

                configEmbed.addFields([
                    {
                        name: '📢 Webhook Notifications',
                        value: webhookFields.length > 0 ? webhookFields.join('\n') : '❌ No webhooks configured',
                        inline: true
                    }
                ]);
            } else {
                configEmbed.addFields([
                    {
                        name: '📢 Webhook Notifications',
                        value: '❌ No stock notifications configured',
                        inline: true
                    }
                ]);
            }

            // Add Role Configuration
            if (roleConfig) {
                const roleFields = [];
                if (roleConfig.seedRoles?.size > 0) roleFields.push(`🌱 Seeds: ${roleConfig.seedRoles.size} roles`);
                if (roleConfig.gearRoles?.size > 0) roleFields.push(`⚙️ Gears: ${roleConfig.gearRoles.size} roles`);
                if (roleConfig.eggRoles?.size > 0) roleFields.push(`🥚 Eggs: ${roleConfig.eggRoles.size} roles`);
                if (roleConfig.weatherRoles?.size > 0) roleFields.push(`🌦️ Weather: ${roleConfig.weatherRoles.size} roles`);
                if (roleConfig.eventRoles?.size > 0) roleFields.push(`🎉 Events: ${roleConfig.eventRoles.size} roles`);
                if (roleConfig.merchantRoles?.size > 0) roleFields.push(`💰 Merchant: ${roleConfig.merchantRoles.size} roles`);
                if (roleConfig.cosmeticRole) roleFields.push(`🎁 Cosmetic: <@&${roleConfig.cosmeticRole}>`);

                configEmbed.addFields([
                    {
                        name: '🎭 Role Pings',
                        value: roleFields.length > 0 ? roleFields.join('\n') : '❌ No role pings configured',
                        inline: true
                    }
                ]);
            } else {
                configEmbed.addFields([
                    {
                        name: '🎭 Role Pings',
                        value: '❌ No role configurations found',
                        inline: true
                    }
                ]);
            }

            // Add Auto Reaction Configuration
            if (autoReactionConfig) {
                const reactionFields = [];
                if (autoReactionConfig.seedsGearAutoReaction) reactionFields.push('✅ Seeds/Gears');
                if (autoReactionConfig.eggAutoReaction) reactionFields.push('✅ Eggs');
                if (autoReactionConfig.eventAutoReaction) reactionFields.push('✅ Events');
                if (autoReactionConfig.cosmeticAutoReaction) reactionFields.push('✅ Cosmetics');
                if (autoReactionConfig.merchantAutoReaction) reactionFields.push('✅ Merchant');

                configEmbed.addFields([
                    {
                        name: '👍 Auto Reactions',
                        value: reactionFields.length > 0 ? reactionFields.join('\n') : '❌ No auto reactions enabled',
                        inline: true
                    }
                ]);
            } else {
                configEmbed.addFields([
                    {
                        name: '👍 Auto Reactions',
                        value: '❌ No auto reaction config found',
                        inline: true
                    }
                ]);
            }

            // Add raw data button for detailed inspection
            const rawDataButton = new ButtonBuilder()
                .setCustomId(`raw-config-${guildId}`)
                .setLabel('View Raw Data')
                .setStyle(ButtonStyle.Secondary);

            const actionRow = new ActionRowBuilder().addComponents(rawDataButton);

            await message.reply({
                embeds: [configEmbed],
                components: [actionRow]
            });

        } catch (error) {
            console.error('Error fetching server config:', error);
            await message.reply(`❌ An error occurred while fetching configuration for guild \`${guildId}\`: ${error.message}`);
        }
    }
};