const {
    SlashCommandBuilder,
    PermissionFlagsBits,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    EmbedBuilder,
    MessageFlags,
    ChannelSelectMenuBuilder,
    RoleSelectMenuBuilder,
    ChannelType
} = require('discord.js');
const StockNotification = require('../../models/StockNotification');
const SeedRoleConfig = require('../../models/roleConfig');
const AutoReactionConfig = require('../../models/autoReactionConfig');
const { createWebhook, deleteWebhook, getWebhookName, getWebhookAvatar } = require('../../utils/webhookManager');
const path = require('path');
const fs = require('fs');

// Helper functions to get fresh data each time (not cached)
const getAvailableFruits = () => {
    const allItems = JSON.parse(fs.readFileSync('src/data/allItems.json', 'utf8'));
    return allItems.filter(item =>
        item.category === 'Fruits' &&
        item.stockable === true
    );
};

const getAvailableGear = () => {
    const allItems = JSON.parse(fs.readFileSync('src/data/allItems.json', 'utf8'));
    return allItems.filter(item =>
        item.category === 'Gears' &&
        item.stockable === true
    );
};

const getAvailableEggs = () => {
    const allItems = JSON.parse(fs.readFileSync('src/data/allItems.json', 'utf8'));
    return allItems.filter(item =>
        item.category === 'Eggs' &&
        item.stockable === true
    );
};

const getAvailableWeather = () => {
    try {
        const weatherDataPath = 'src/data/weatherData.json';
        const fileContent = fs.readFileSync(weatherDataPath, 'utf8');

        // Check if file is empty or contains only whitespace
        if (!fileContent.trim()) {
            console.warn(`Weather data file is empty: ${weatherDataPath}`);
            return [];
        }

        const weatherData = JSON.parse(fileContent);

        // Ensure weatherData is an array
        if (!Array.isArray(weatherData)) {
            console.warn(`Weather data is not an array: ${weatherDataPath}`);
            return [];
        }

        return weatherData.map(w => ({
            name: w.name || w.apiName || 'Unknown',
            apiName: w.apiName || w.name || 'unknown',
            effects: w.effects || []
        }));
    } catch (error) {
        console.error('Error reading weather data:', error.message);
        return [];
    }
};

const getAvailableEvents = () => {
    const allItems = JSON.parse(fs.readFileSync('src/data/allItems.json', 'utf8'));
    return allItems.filter(item =>
        item.category === 'Events' &&
        item.stockable === true
    );
};

const getAvailableMerchantTypes = () => {
    const allItems = JSON.parse(fs.readFileSync('src/data/allItems.json', 'utf8'));
    return [...new Set(allItems
        .filter(item => item.category && item.category.includes('Merchant'))
        .map(item => item.category)
    )];
};

const setupSeed = async (interaction, index) => {
    const availableFruits = getAvailableFruits(); // Get fresh data

    if (index >= availableFruits.length) {
        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Role Setup Complete')
                .setDescription('All seed roles have been configured.')],
            components: [backRow]
        });
        return;
    }

    const seed = availableFruits[index];
    const roleConfig = await SeedRoleConfig.findOne({ guildId: interaction.guildId });
    const currentRoleId = roleConfig?.seedRoles?.get(seed.name);

    const roleSelect = new RoleSelectMenuBuilder()
        .setCustomId(`role-select-${index}`)
        .setPlaceholder('Select a role for ' + seed.name);

    const nextButton = new ButtonBuilder()
        .setCustomId(`nextRoleMenu-${index}`)
        .setLabel(index === availableFruits.length - 1 ? 'Finish' : 'Next')
        .setStyle(ButtonStyle.Primary);

    const backButton = new ButtonBuilder()
        .setCustomId(`backRoleMenu-${index}`)
        .setLabel('Back')
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(index === 0);

    const removeButton = new ButtonBuilder()
        .setCustomId(`remove-seed-role-${index}`)
        .setLabel('Remove Role')
        .setStyle(ButtonStyle.Danger)
        .setDisabled(!currentRoleId);

    const mainMenuButton = new ButtonBuilder()
        .setCustomId('main-menu')
        .setLabel('Back to Main Menu')
        .setStyle(ButtonStyle.Secondary);

    const roleRow = new ActionRowBuilder().addComponents(roleSelect);
    const navRow = new ActionRowBuilder().addComponents(backButton, nextButton);
    const removeRow = new ActionRowBuilder().addComponents(removeButton, mainMenuButton);

    const embed = new EmbedBuilder()
        .setColor('#f1fbd2')
        .setTitle('Configure Role Pings')
        .setDescription(`Select a role to ping when ${seed.name} is in stock`)
        .addFields([
            { name: 'Current Role', value: currentRoleId ? `<@&${currentRoleId}>` : 'None', inline: true },
            { name: 'Progress', value: `${index + 1}/${availableFruits.length}`, inline: true }
        ]);

    await interaction.update({
        embeds: [embed],
        components: [roleRow, navRow, removeRow]
    });
};

const setupGear = async (interaction, index) => {
    const availableGear = getAvailableGear(); // Get fresh data

    if (index >= availableGear.length) {
        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Gear Role Setup Complete')
                .setDescription('All gear roles have been configured.')],
            components: [backRow]
        });
        return;
    }

    const gear = availableGear[index];
    const roleConfig = await SeedRoleConfig.findOne({ guildId: interaction.guildId });
    const currentRoleId = roleConfig?.gearRoles?.get(gear.name);

    const roleSelect = new RoleSelectMenuBuilder()
        .setCustomId(`gear-role-select-${index}`)
        .setPlaceholder('Select a role for ' + gear.name);

    const nextButton = new ButtonBuilder()
        .setCustomId(`nextGearRoleMenu-${index}`)
        .setLabel(index === availableGear.length - 1 ? 'Finish' : 'Next')
        .setStyle(ButtonStyle.Primary);

    const backButton = new ButtonBuilder()
        .setCustomId(`backGearRoleMenu-${index}`)
        .setLabel('Back')
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(index === 0);

    const removeButton = new ButtonBuilder()
        .setCustomId(`remove-gear-role-${index}`)
        .setLabel('Remove Role')
        .setStyle(ButtonStyle.Danger)
        .setDisabled(!currentRoleId);

    const mainMenuButton = new ButtonBuilder()
        .setCustomId('main-menu')
        .setLabel('Back to Main Menu')
        .setStyle(ButtonStyle.Secondary);

    const roleRow = new ActionRowBuilder().addComponents(roleSelect);
    const navRow = new ActionRowBuilder().addComponents(backButton, nextButton);
    const removeRow = new ActionRowBuilder().addComponents(removeButton, mainMenuButton);

    const embed = new EmbedBuilder()
        .setColor('#f1fbd2')
        .setTitle('Configure Gear Role Pings')
        .setDescription(`Select a role to ping when ${gear.name} is in stock`)
        .addFields([
            { name: 'Current Role', value: currentRoleId ? `<@&${currentRoleId}>` : 'None', inline: true },
            { name: 'Progress', value: `${index + 1}/${availableGear.length}`, inline: true }
        ]);

    await interaction.update({
        embeds: [embed],
        components: [roleRow, navRow, removeRow]
    });
};

const setupEgg = async (interaction, index) => {
    const availableEggs = getAvailableEggs(); // Get fresh data

    if (index >= availableEggs.length) {
        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Egg Role Setup Complete')
                .setDescription('All egg roles have been configured.')],
            components: [backRow]
        });
        return;
    }

    const egg = availableEggs[index];
    const roleConfig = await SeedRoleConfig.findOne({ guildId: interaction.guildId });
    const currentRoleId = roleConfig?.eggRoles?.get(egg.name);

    const roleSelect = new RoleSelectMenuBuilder()
        .setCustomId(`egg-role-select-${index}`)
        .setPlaceholder('Select a role for ' + egg.name);

    const nextButton = new ButtonBuilder()
        .setCustomId(`nextEggRoleMenu-${index}`)
        .setLabel(index === availableEggs.length - 1 ? 'Finish' : 'Next')
        .setStyle(ButtonStyle.Primary);

    const backButton = new ButtonBuilder()
        .setCustomId(`backEggRoleMenu-${index}`)
        .setLabel('Back')
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(index === 0);

    const removeButton = new ButtonBuilder()
        .setCustomId(`remove-egg-role-${index}`)
        .setLabel('Remove Role')
        .setStyle(ButtonStyle.Danger)
        .setDisabled(!currentRoleId);

    const mainMenuButton = new ButtonBuilder()
        .setCustomId('main-menu')
        .setLabel('Back to Main Menu')
        .setStyle(ButtonStyle.Secondary);

    const roleRow = new ActionRowBuilder().addComponents(roleSelect);
    const navRow = new ActionRowBuilder().addComponents(backButton, nextButton);
    const removeRow = new ActionRowBuilder().addComponents(removeButton);
    const menuRow = new ActionRowBuilder().addComponents(mainMenuButton);

    const embed = new EmbedBuilder()
        .setColor('#f1fbd2')
        .setTitle('Configure Egg Role Pings')
        .setDescription(`Select a role to ping when ${egg.name} is in stock`)
        .addFields([
            { name: 'Current Role', value: currentRoleId ? `<@&${currentRoleId}>` : 'None', inline: true },
            { name: 'Progress', value: `${index + 1}/${availableEggs.length}`, inline: true }
        ]);

    await interaction.update({
        embeds: [embed],
        components: [roleRow, navRow, removeRow, menuRow]
    });
};

const setupCosmetic = async (interaction) => {
    const roleConfig = await SeedRoleConfig.findOne({ guildId: interaction.guildId }) || new SeedRoleConfig({ guildId: interaction.guildId });
    const currentRoleId = roleConfig.cosmeticRole;

    const roleSelect = new RoleSelectMenuBuilder()
        .setCustomId('cosmetic-role-select')
        .setPlaceholder('Select a role for all cosmetics');

    const removeButton = new ButtonBuilder()
        .setCustomId('remove-cosmetic-role')
        .setLabel('Remove Role')
        .setStyle(ButtonStyle.Danger)
        .setDisabled(!currentRoleId);

    const mainMenuButton = new ButtonBuilder()
        .setCustomId('main-menu')
        .setLabel('Back to Main Menu')
        .setStyle(ButtonStyle.Secondary);

    const roleRow = new ActionRowBuilder().addComponents(roleSelect);
    const buttonRow = new ActionRowBuilder().addComponents(removeButton, mainMenuButton);

    const embed = new EmbedBuilder()
        .setColor('#f1fbd2')
        .setTitle('Configure Cosmetic Role Ping')
        .setDescription('Select a role to ping when any cosmetic is in stock.')
        .addFields([
            { name: 'Current Role', value: currentRoleId ? `<@&${currentRoleId}>` : 'None', inline: true }
        ]);

    await interaction.update({
        embeds: [embed],
        components: [roleRow, buttonRow]
    });
};

const setupMerchant = async (interaction, index) => {
    const availableMerchantTypes = getAvailableMerchantTypes(); // Get fresh data

    if (index >= availableMerchantTypes.length) {
        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Role Setup Complete')
                .setDescription('All merchant roles have been configured.')],
            components: [backRow]
        });
        return;
    }

    const merchantType = availableMerchantTypes[index];
    const roleConfig = await SeedRoleConfig.findOne({ guildId: interaction.guildId });
    const currentRoleId = roleConfig?.merchantRoles?.get(merchantType);

    const roleSelect = new RoleSelectMenuBuilder()
        .setCustomId(`merchant-role-select-${index}`)
        .setPlaceholder(`Select a role for ${merchantType}`);

    const nextButton = new ButtonBuilder()
        .setCustomId(`nextMerchantRoleMenu-${index}`)
        .setLabel(index === availableMerchantTypes.length - 1 ? 'Finish' : 'Next')
        .setStyle(ButtonStyle.Primary);

    const backButton = new ButtonBuilder()
        .setCustomId(`backMerchantRoleMenu-${index}`)
        .setLabel('Back')
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(index === 0);

    const removeButton = new ButtonBuilder()
        .setCustomId(`remove-merchant-role-${index}`)
        .setLabel('Remove Role')
        .setStyle(ButtonStyle.Danger)
        .setDisabled(!currentRoleId);

    const mainMenuButton = new ButtonBuilder()
        .setCustomId('main-menu')
        .setLabel('Back to Main Menu')
        .setStyle(ButtonStyle.Secondary);

    const roleRow = new ActionRowBuilder().addComponents(roleSelect);
    const navRow = new ActionRowBuilder().addComponents(backButton, nextButton);
    const removeRow = new ActionRowBuilder().addComponents(removeButton, mainMenuButton);

    const emoji = merchantType === 'Sky Merchant' ? '☁️' : merchantType === 'Gnome Merchant' ? '🧙' : '💰';

    const embed = new EmbedBuilder()
        .setColor('#f1fbd2')
        .setTitle(`Configure ${merchantType} Role`)
        .setDescription(`Select a role to ping when the ${merchantType} arrives.`)
        .addFields([
            { name: 'Current Role', value: currentRoleId ? `<@&${currentRoleId}>` : 'None', inline: true },
            { name: 'Progress', value: `${index + 1}/${availableMerchantTypes.length}`, inline: true }
        ]);

    await interaction.update({
        embeds: [embed],
        components: [roleRow, navRow, removeRow]
    });
};

const setupWeather = async (interaction, index) => {
    const availableWeather = getAvailableWeather(); // Get fresh data

    // If no weather data available, show error
    if (availableWeather.length === 0) {
        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Weather Data Error')
                .setDescription('Unable to load weather data. Please check the weather data files.')],
            components: [backRow]
        });
        return;
    }

    if (index >= availableWeather.length) {
        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Weather Role Setup Complete')
                .setDescription('All weather roles have been configured.')],
            components: [backRow]
        });
        return;
    }

    const weather = availableWeather[index];
    const roleConfig = await SeedRoleConfig.findOne({ guildId: interaction.guildId });
    const currentRoleId = roleConfig?.weatherRoles?.get(weather.apiName);

    const roleSelect = new RoleSelectMenuBuilder()
        .setCustomId(`weather-role-select-${index}`)
        .setPlaceholder('Select a role for ' + weather.name);

    const nextButton = new ButtonBuilder()
        .setCustomId(`nextWeatherRoleMenu-${index}`)
        .setLabel(index === availableWeather.length - 1 ? 'Finish' : 'Next')
        .setStyle(ButtonStyle.Primary);

    const backButton = new ButtonBuilder()
        .setCustomId(`backWeatherRoleMenu-${index}`)
        .setLabel('Back')
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(index === 0);

    const removeButton = new ButtonBuilder()
        .setCustomId(`remove-weather-role-${index}`)
        .setLabel('Remove Role')
        .setStyle(ButtonStyle.Danger)
        .setDisabled(!currentRoleId);

    const mainMenuButton = new ButtonBuilder()
        .setCustomId('main-menu')
        .setLabel('Back to Main Menu')
        .setStyle(ButtonStyle.Secondary);

    const roleRow = new ActionRowBuilder().addComponents(roleSelect);
    const navRow = new ActionRowBuilder().addComponents(backButton, nextButton);
    const removeRow = new ActionRowBuilder().addComponents(removeButton);
    const menuRow = new ActionRowBuilder().addComponents(mainMenuButton);

    const embed = new EmbedBuilder()
        .setColor('#f1fbd2')
        .setTitle('Configure Weather Role Pings')
        .setDescription(`Select a role to ping when **${weather.name}** weather is active.`)
        .addFields([
            { name: 'Current Role', value: currentRoleId ? `<@&${currentRoleId}>` : 'None', inline: true },
            { name: 'Progress', value: `${index + 1}/${availableWeather.length}`, inline: true },
            { name: 'Effects', value: weather.effects.join('\n'), inline: false }
        ]);

    await interaction.update({
        embeds: [embed],
        components: [roleRow, navRow, removeRow, menuRow]
    });
};

const setupEvent = async (interaction, index) => {
    const availableEvents = getAvailableEvents(); // Get fresh data

    if (index >= availableEvents.length) {
        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);
        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Event Role Setup Complete')
                .setDescription('All event roles have been configured.')],
            components: [backRow]
        });
        return;
    }
    const eventItem = availableEvents[index];
    const roleConfig = await SeedRoleConfig.findOne({ guildId: interaction.guildId });
    const currentRoleId = roleConfig?.eventRoles?.get(eventItem.name);
    const roleSelect = new RoleSelectMenuBuilder()
        .setCustomId(`event-role-select-${index}`)
        .setPlaceholder('Select a role for ' + eventItem.name);
    const nextButton = new ButtonBuilder()
        .setCustomId(`nextEventRoleMenu-${index}`)
        .setLabel(index === availableEvents.length - 1 ? 'Finish' : 'Next')
        .setStyle(ButtonStyle.Primary);
    const backButton = new ButtonBuilder()
        .setCustomId(`backEventRoleMenu-${index}`)
        .setLabel('Back')
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(index === 0);
    const removeButton = new ButtonBuilder()
        .setCustomId(`remove-event-role-${index}`)
        .setLabel('Remove Role')
        .setStyle(ButtonStyle.Danger)
        .setDisabled(!currentRoleId);
    const mainMenuButton = new ButtonBuilder()
        .setCustomId('main-menu')
        .setLabel('Back to Main Menu')
        .setStyle(ButtonStyle.Secondary);
    const roleRow = new ActionRowBuilder().addComponents(roleSelect);
    const navRow = new ActionRowBuilder().addComponents(backButton, nextButton);
    const removeRow = new ActionRowBuilder().addComponents(removeButton);
    const menuRow = new ActionRowBuilder().addComponents(mainMenuButton);
    const embed = new EmbedBuilder()
        .setColor('#f1fbd2')
        .setTitle('Configure Event Role Pings')
        .setDescription(`Select a role to ping when ${eventItem.name} is in the event shop`)
        .addFields([
            { name: 'Current Role', value: currentRoleId ? `<@&${currentRoleId}>` : 'None', inline: true },
            { name: 'Progress', value: `${index + 1}/${availableEvents.length}`, inline: true }
        ]);
    await interaction.update({
        embeds: [embed],
        components: [roleRow, navRow, removeRow, menuRow]
    });
};

module.exports = async (interaction, c, client, handler) => {
    if (!interaction.customId) return;

    const notification = await StockNotification.findOne({ guildId: interaction.guildId });
    let roleConfig = await SeedRoleConfig.findOne({ guildId: interaction.guildId });

    const seedsGearButton = new ButtonBuilder()
        .setCustomId('seedsGearNavBtn')
        .setLabel('Seeds/Gear')
        .setStyle(ButtonStyle.Primary);

    const eggsButton = new ButtonBuilder()
        .setCustomId('eggsNavBtn')
        .setLabel('Eggs')
        .setStyle(ButtonStyle.Primary);

    const weatherButton = new ButtonBuilder()
        .setCustomId('weatherNavBtn')
        .setLabel('Weather')
        .setStyle(ButtonStyle.Primary);

    const eventButton = new ButtonBuilder()
        .setCustomId('eventNavBtn')
        .setLabel('Event Shop')
        .setStyle(ButtonStyle.Primary);

    const cosmeticButton = new ButtonBuilder()
        .setCustomId('cosmeticNavBtn')
        .setLabel('Cosmetics')
        .setStyle(ButtonStyle.Primary);

    const merchantButton = new ButtonBuilder()
        .setCustomId('merchantNavBtn')
        .setLabel('Merchant')
        .setStyle(ButtonStyle.Primary);

    const removeButton = new ButtonBuilder()
        .setCustomId('removeConfigBtn')
        .setLabel('Remove All Settings')
        .setStyle(ButtonStyle.Danger);

    const mainRow = new ActionRowBuilder().addComponents(seedsGearButton, eggsButton, weatherButton);
    const mainRow2 = new ActionRowBuilder().addComponents(eventButton, cosmeticButton, merchantButton);
    const mainRow3 = new ActionRowBuilder().addComponents(removeButton);

    const mainEmbed = new EmbedBuilder()
        .setColor('#f1fbd2')
        .setTitle('Stock Notification Configuration')
        .setDescription('Select an option to configure your stock notification settings.')
        .addFields([
            {
                name: '📢 Seeds & Gears Notifications',
                value: notification && notification.webhookUrl ? '✅ Webhook configured' : '❌ Not configured',
                inline: true
            },
            {
                name: '🥚 Egg Notifications',
                value: notification && notification.eggWebhookUrl ? '✅ Webhook configured' : '❌ Not configured',
                inline: true
            },
            {
                name: '🌦️ Weather Notifications',
                value: notification && notification.weatherWebhookUrl ? '✅ Webhook configured' : '❌ Not configured',
                inline: true
            },
            {
                name: '🎉 Event Shop Notifications',
                value: notification && notification.eventWebhookUrl ? '✅ Webhook configured' : '❌ Not configured',
                inline: true
            },
            {
                name: '🎁 Cosmetic Notifications',
                value: notification && notification.cosmeticWebhookUrl ? '✅ Webhook configured' : '❌ Not configured',
                inline: true
            },
            {
                name: '💰 Merchant Notifications',
                value: notification && notification.merchantWebhookUrl ? '✅ Webhook configured' : '❌ Not configured',
                inline: true
            },
            {
                name: '🔔 Seed Role Pings',
                value: roleConfig ? `${roleConfig.seedRoles.size} roles configured` : 'Not configured',
                inline: true
            },
            {
                name: '🛠️ Gear Role Pings',
                value: roleConfig ? `${roleConfig.gearRoles?.size || 0} roles configured` : 'Not configured',
                inline: true
            },
            {
                name: '🥚 Egg Role Pings',
                value: roleConfig ? `${roleConfig.eggRoles?.size || 0} roles configured` : 'Not configured',
                inline: true
            },
            {
                name: '🌤️ Weather Role Pings',
                value: roleConfig ? `${roleConfig.weatherRoles?.size || 0} roles configured` : 'Not configured',
                inline: true
            },
            {
                name: '🎉 Event Role Pings',
                value: roleConfig ? `${roleConfig.eventRoles?.size || 0} roles configured` : 'Not configured',
                inline: true
            },
            {
                name: '🎁 Cosmetic Role Ping',
                value: roleConfig && roleConfig.cosmeticRole ? `<@&${roleConfig.cosmeticRole}>` : 'Not configured',
                inline: true
            },
            {
                name: '💰 Merchant Role Pings',
                value: roleConfig && roleConfig.merchantRoles ? `${roleConfig.merchantRoles.size || 0} roles configured` : 'Not configured',
                inline: true
            }
        ]);

    if (interaction.customId === 'seedsGearNavBtn') {
        const channelButton = new ButtonBuilder()
            .setCustomId('channelConfigBtn')
            .setLabel('Notification Channel')
            .setStyle(ButtonStyle.Primary);
        const rolesButton = new ButtonBuilder()
            .setCustomId('seedsGearRoleConfigBtn')
            .setLabel('Role Pings')
            .setStyle(ButtonStyle.Secondary);
        const autoReactionButton = new ButtonBuilder()
            .setCustomId('seedsGearAutoReactionBtn')
            .setLabel('Auto-Reactions')
            .setStyle(ButtonStyle.Secondary);
        const removeButton = new ButtonBuilder()
            .setCustomId('remove-seedsgear-settings')
            .setLabel('Remove Category Settings')
            .setStyle(ButtonStyle.Danger);
        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const row1 = new ActionRowBuilder().addComponents(channelButton, rolesButton);
        const row2 = new ActionRowBuilder().addComponents(removeButton, backButton);
        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Seeds/Gear Settings')
                .setDescription('Configure notification channel, role pings, or auto-reactions for seeds and gear.')],
            components: [row1, row2]
        });
        return;
    } else if (interaction.customId === 'eggsNavBtn') {
        const channelButton = new ButtonBuilder()
            .setCustomId('eggChannelConfigBtn')
            .setLabel('Notification Channel')
            .setStyle(ButtonStyle.Primary);
        const rolesButton = new ButtonBuilder()
            .setCustomId('eggRolesConfigBtn')
            .setLabel('Role Pings')
            .setStyle(ButtonStyle.Secondary);
        const autoReactionButton = new ButtonBuilder()
            .setCustomId('eggAutoReactionBtn')
            .setLabel('Auto-Reactions')
            .setStyle(ButtonStyle.Secondary);
        const removeButton = new ButtonBuilder()
            .setCustomId('remove-eggs-settings')
            .setLabel('Remove Category Settings')
            .setStyle(ButtonStyle.Danger);
        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const row1 = new ActionRowBuilder().addComponents(channelButton, rolesButton);
        const row2 = new ActionRowBuilder().addComponents(removeButton, backButton);
        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Eggs Settings')
                .setDescription('Configure notification channel, role pings, or auto-reactions for eggs.')],
            components: [row1, row2]
        });
        return;
    } else if (interaction.customId === 'weatherNavBtn') {
        const channelButton = new ButtonBuilder()
            .setCustomId('weatherChannelConfigBtn')
            .setLabel('Notification Channel')
            .setStyle(ButtonStyle.Primary);
        const rolesButton = new ButtonBuilder()
            .setCustomId('weatherRolesConfigBtn')
            .setLabel('Role Pings')
            .setStyle(ButtonStyle.Secondary);
        const removeButton = new ButtonBuilder()
            .setCustomId('remove-weather-settings')
            .setLabel('Remove Category Settings')
            .setStyle(ButtonStyle.Danger);
        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const row1 = new ActionRowBuilder().addComponents(channelButton, rolesButton);
        const row2 = new ActionRowBuilder().addComponents(removeButton, backButton);
        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Weather Settings')
                .setDescription('Configure notification channel, role pings, or auto-reactions for weather events.')],
            components: [row1, row2]
        });
        return;
    } else if (interaction.customId === 'eventNavBtn') {
        const channelButton = new ButtonBuilder()
            .setCustomId('eventChannelConfigBtn')
            .setLabel('Notification Channel')
            .setStyle(ButtonStyle.Primary);
        const rolesButton = new ButtonBuilder()
            .setCustomId('eventRolesConfigBtn')
            .setLabel('Role Pings')
            .setStyle(ButtonStyle.Secondary);
        const autoReactionButton = new ButtonBuilder()
            .setCustomId('eventAutoReactionBtn')
            .setLabel('Auto-Reactions')
            .setStyle(ButtonStyle.Secondary);
        const removeButton = new ButtonBuilder()
            .setCustomId('remove-events-settings')
            .setLabel('Remove Category Settings')
            .setStyle(ButtonStyle.Danger);
        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const row1 = new ActionRowBuilder().addComponents(channelButton, rolesButton);
        const row2 = new ActionRowBuilder().addComponents(removeButton, backButton);
        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Event Shop Settings')
                .setDescription('Configure notification channel, role pings, or auto-reactions for event shop.')],
            components: [row1, row2]
        });
        return;
    } else if (interaction.customId === 'cosmeticNavBtn') {
        const channelButton = new ButtonBuilder()
            .setCustomId('cosmeticChannelConfigBtn')
            .setLabel('Notification Channel')
            .setStyle(ButtonStyle.Primary);

        const roleButton = new ButtonBuilder()
            .setCustomId('cosmeticRoleConfigBtn')
            .setLabel('Role Ping')
            .setStyle(ButtonStyle.Secondary);

        const autoReactionButton = new ButtonBuilder()
            .setCustomId('cosmeticAutoReactionBtn')
            .setLabel('Auto-Reactions')
            .setStyle(ButtonStyle.Secondary);

        const removeButton = new ButtonBuilder()
            .setCustomId('remove-cosmetics-settings')
            .setLabel('Remove Category Settings')
            .setStyle(ButtonStyle.Danger);
        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const row1 = new ActionRowBuilder().addComponents(channelButton, roleButton);
        const row2 = new ActionRowBuilder().addComponents(removeButton, backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Cosmetic Settings')
                .setDescription('Configure notification channel, role ping, or auto-reactions for cosmetics.')],
            components: [row1, row2]
        });
        return;
    } else if (interaction.customId === 'merchantNavBtn') {
        const channelButton = new ButtonBuilder()
            .setCustomId('merchantChannelConfigBtn')
            .setLabel('Notification Channel')
            .setStyle(ButtonStyle.Primary);

        const roleButton = new ButtonBuilder()
            .setCustomId('merchantRoleConfigBtn')
            .setLabel('Role Ping')
            .setStyle(ButtonStyle.Secondary);

        const autoReactionButton = new ButtonBuilder()
            .setCustomId('merchantAutoReactionBtn')
            .setLabel('Auto-Reactions')
            .setStyle(ButtonStyle.Secondary);

        const removeButton = new ButtonBuilder()
            .setCustomId('remove-merchant-settings')
            .setLabel('Remove Category Settings')
            .setStyle(ButtonStyle.Danger);
        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const row1 = new ActionRowBuilder().addComponents(channelButton, roleButton);
        const row2 = new ActionRowBuilder().addComponents(removeButton, backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Merchant Settings')
                .setDescription('Configure notification channel, role ping, or auto-reactions for traveling merchant.')],
            components: [row1, row2]
        });
        return;
    } else if (interaction.customId === 'cosmeticChannelConfigBtn') {
        const channelSelect = new ChannelSelectMenuBuilder()
            .setCustomId('cosmetic-channel-select')
            .setPlaceholder('Select a channel for cosmetic notifications')
            .setChannelTypes([ChannelType.GuildText, ChannelType.GuildAnnouncement]);

        const removeButton = new ButtonBuilder()
            .setCustomId('remove-cosmetic-channel')
            .setLabel('Remove Channel')
            .setStyle(ButtonStyle.Danger)
            .setDisabled(!notification || !notification.cosmeticWebhookUrl);

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const channelRow = new ActionRowBuilder().addComponents(channelSelect);
        const buttonRow = new ActionRowBuilder().addComponents(removeButton, backButton);

        const embed = new EmbedBuilder()
            .setColor('#f1fbd2')
            .setTitle('Set Cosmetic Notification Channel')
            .setDescription('Select a channel where cosmetic notifications will be sent.');

        if (notification && notification.cosmeticWebhookUrl) {
            embed.addFields({ name: 'Current Status', value: '✅ Webhook configured', inline: true });
        }

        await interaction.update({
            embeds: [embed],
            components: [channelRow, buttonRow]
        });
    } else if (interaction.customId === 'cosmetic-channel-select') {
        const channel = interaction.channels.first();
    
        const botMember = interaction.guild.members.me;
        const botPermissions = channel.permissionsFor(botMember);
    
        if (!botPermissions || !botPermissions.has('SendMessages') || !botPermissions.has('ManageWebhooks')) {
            const backButton = new ButtonBuilder()
                .setCustomId('main-menu')
                .setLabel('Back')
                .setStyle(ButtonStyle.Secondary);

            const backRow = new ActionRowBuilder().addComponents(backButton);

            const missingPerms = [];
            if (!botPermissions || !botPermissions.has('SendMessages')) missingPerms.push('Send Messages');
            if (!botPermissions || !botPermissions.has('ManageWebhooks')) missingPerms.push('Manage Webhooks');

            const errorEmbed = new EmbedBuilder()
                .setColor('#ff4d4d')
                .setTitle('Permission Error')
                .setDescription(`I don't have the following permissions in ${channel}: ${missingPerms.join(', ')}. Please choose another channel or update my permissions.`);

            await interaction.update({
                embeds: [errorEmbed],
                components: [backRow]
            });

            return;
        }

        // Get existing config to check for old webhook
        const existingConfig = await StockNotification.findOne({ guildId: interaction.guildId });

        // Delete old webhook if it exists
        if (existingConfig?.cosmeticWebhookUrl) {
            await deleteWebhook(existingConfig.cosmeticWebhookUrl);
        }

        // Create new webhook
        const webhookUrl = await createWebhook(channel, getWebhookName('cosmetics'), getWebhookAvatar());

        if (!webhookUrl) {
            const backButton = new ButtonBuilder()
                .setCustomId('main-menu')
                .setLabel('Back')
                .setStyle(ButtonStyle.Secondary);
            const backRow = new ActionRowBuilder().addComponents(backButton);
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff4d4d')
                .setTitle('Webhook Creation Failed')
                .setDescription(`Failed to create webhook in ${channel}. Please try again or contact support.`);
            await interaction.update({
                embeds: [errorEmbed],
                components: [backRow]
            });
            return;
        }

        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            {
                guildId: interaction.guildId,
                cosmeticWebhookUrl: webhookUrl
            },
            { upsert: true }
        );

        confirmationEmbed = new EmbedBuilder()
            .setTitle("Cosmetic Stock")
            .setDescription("This channel has been configured for cosmetic stock updates. Please wait until the next restock for the first post. Cosmetics restock every 4 hours.")
            .setThumbnail("https://i.imgur.com/xPnNwfi.png")
            .setColor("#f1fbd2")

        try {
            channel.send({ embeds: [confirmationEmbed] });
        } catch (error) {

        }
    
        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
    
        const backRow = new ActionRowBuilder().addComponents(backButton);
    
        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Channel Set')
                .setDescription(`Notifications will now be sent to ${channel}`)],
            components: [backRow]
        });
    } else if (interaction.customId === 'remove-cosmetic-channel') {
        // Get existing config to delete webhook
        const existingConfig = await StockNotification.findOne({ guildId: interaction.guildId });

        // Delete webhook if it exists
        if (existingConfig?.cosmeticWebhookUrl) {
            await deleteWebhook(existingConfig.cosmeticWebhookUrl);
        }

        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            { $unset: { cosmeticWebhookUrl: 1 } }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Cosmetic Channel Removed')
                .setDescription('Cosmetic notification channel has been removed.')],
            components: [backRow]
        });
    } else if (interaction.customId === 'merchantChannelConfigBtn') {
        const channelSelect = new ChannelSelectMenuBuilder()
            .setCustomId('merchant-channel-select')
            .setPlaceholder('Select a channel for merchant notifications')
            .setChannelTypes([ChannelType.GuildText, ChannelType.GuildAnnouncement]);

        const removeButton = new ButtonBuilder()
            .setCustomId('remove-merchant-channel')
            .setLabel('Remove Channel')
            .setStyle(ButtonStyle.Danger)
            .setDisabled(!notification || !notification.merchantWebhookUrl);

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const channelRow = new ActionRowBuilder().addComponents(channelSelect);
        const buttonRow = new ActionRowBuilder().addComponents(removeButton, backButton);

        const embed = new EmbedBuilder()
            .setColor('#f1fbd2')
            .setTitle('Set Merchant Notification Channel')
            .setDescription('Select a channel where traveling merchant notifications will be sent.');

        if (notification && notification.merchantWebhookUrl) {
            embed.addFields({ name: 'Current Status', value: '✅ Webhook configured', inline: true });
        }

        await interaction.update({
            embeds: [embed],
            components: [channelRow, buttonRow]
        });
    } else if (interaction.customId === 'merchant-channel-select') {
        const channel = interaction.channels.first();

        const botMember = interaction.guild.members.me;
        const botPermissions = channel.permissionsFor(botMember);

        if (!botPermissions || !botPermissions.has('SendMessages') || !botPermissions.has('ManageWebhooks')) {
            const backButton = new ButtonBuilder()
                .setCustomId('main-menu')
                .setLabel('Back')
                .setStyle(ButtonStyle.Secondary);

            const backRow = new ActionRowBuilder().addComponents(backButton);

            const missingPerms = [];
            if (!botPermissions || !botPermissions.has('SendMessages')) missingPerms.push('Send Messages');
            if (!botPermissions || !botPermissions.has('ManageWebhooks')) missingPerms.push('Manage Webhooks');

            const errorEmbed = new EmbedBuilder()
                .setColor('#ff4d4d')
                .setTitle('Permission Error')
                .setDescription(`I don't have the following permissions in ${channel}: ${missingPerms.join(', ')}. Please choose another channel or update my permissions.`);

            await interaction.update({
                embeds: [errorEmbed],
                components: [backRow]
            });

            return;
        }

        // Get existing config to check for old webhook
        const existingConfig = await StockNotification.findOne({ guildId: interaction.guildId });

        // Delete old webhook if it exists
        if (existingConfig?.merchantWebhookUrl) {
            await deleteWebhook(existingConfig.merchantWebhookUrl);
        }

        // Create new webhook
        const webhookUrl = await createWebhook(channel, getWebhookName('merchant'), getWebhookAvatar());

        if (!webhookUrl) {
            const backButton = new ButtonBuilder()
                .setCustomId('main-menu')
                .setLabel('Back')
                .setStyle(ButtonStyle.Secondary);
            const backRow = new ActionRowBuilder().addComponents(backButton);
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff4d4d')
                .setTitle('Webhook Creation Failed')
                .setDescription(`Failed to create webhook in ${channel}. Please try again or contact support.`);
            await interaction.update({
                embeds: [errorEmbed],
                components: [backRow]
            });
            return;
        }

        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            {
                guildId: interaction.guildId,
                merchantWebhookUrl: webhookUrl
            },
            { upsert: true }
        );

        confirmationEmbed = new EmbedBuilder()
            .setTitle("Traveling Merchant Stock")
            .setDescription("This channel has been configured for traveling merchant stock updates. Please wait until the next restock for the first post. Merchant restocks every 4 hours.")
            .setThumbnail("https://i.imgur.com/xPnNwfi.png")
            .setColor("#f1fbd2")

        try {
            channel.send({ embeds: [confirmationEmbed] });
        } catch (error) {

        }

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Merchant Channel Set')
                .setDescription(`Traveling merchant notifications will now be sent to ${channel}`)],
            components: [backRow]
        });
    } else if (interaction.customId === 'remove-merchant-channel') {
        // Get existing config to delete webhook
        const existingConfig = await StockNotification.findOne({ guildId: interaction.guildId });

        // Delete webhook if it exists
        if (existingConfig?.merchantWebhookUrl) {
            await deleteWebhook(existingConfig.merchantWebhookUrl);
        }

        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            { $unset: { merchantWebhookUrl: 1 } }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Merchant Channel Removed')
                .setDescription('Merchant notification channel has been removed.')],
            components: [backRow]
        });
    } else if (interaction.customId === 'cosmeticChannelConfigBtn') {
        const channelSelect = new ChannelSelectMenuBuilder()
            .setCustomId('cosmetic-channel-select')
            .setPlaceholder('Select a channel for cosmetic notifications')
            .setChannelTypes([ChannelType.GuildText, ChannelType.GuildAnnouncement]);

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const channelRow = new ActionRowBuilder().addComponents(channelSelect);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Set Notification Channel')
                .setDescription('Select a channel where cosmetic notifications will be sent.')],
            components: [channelRow, backRow]
        });
    } else if (interaction.customId === 'cosmeticRoleConfigBtn') {
        if (!notification || !notification.cosmeticWebhookUrl) {
            await interaction.reply({
                content: 'Please set up a cosmetic notification channel first!',
                flags: MessageFlags.Ephemeral
            });
            return;
        }
        await setupCosmetic(interaction);
    } else if (interaction.customId === 'merchantRoleConfigBtn') {
        if (!notification || !notification.merchantWebhookUrl) {
            await interaction.reply({
                content: 'Please set up a merchant notification channel first!',
                flags: MessageFlags.Ephemeral
            });
            return;
        }
        await setupMerchant(interaction, 0);
    } else if (interaction.customId.startsWith('merchant-role-select-')) {
        const index = parseInt(interaction.customId.split('-')[3]);
        interaction.deferUpdate();
        const availableMerchantTypes = getAvailableMerchantTypes(); // Get fresh data
        const merchantType = availableMerchantTypes[index];
        const roleId = interaction.values[0];

        // Update merchant role using new single role method
        await SeedRoleConfig.updateSingleRole(interaction.guildId, 'merchantRoles', merchantType, roleId);
    } else if (interaction.customId.startsWith('remove-merchant-role-')) {
        const index = parseInt(interaction.customId.split('-')[3]);
        const availableMerchantTypes = getAvailableMerchantTypes(); // Get fresh data
        const merchantType = availableMerchantTypes[index];
        // Remove merchant role using new single role method
        await SeedRoleConfig.deleteSingleRole(interaction.guildId, 'merchantRoles', merchantType);
        await setupMerchant(interaction, index);
    } else if (interaction.customId === 'main-menu') {
        await interaction.update({
            embeds: [mainEmbed],
            components: [mainRow, mainRow2, mainRow3]
        });
        return;
    } else if (interaction.customId === 'channelConfigBtn') {
        const channelSelect = new ChannelSelectMenuBuilder()
            .setCustomId('channel-select')
            .setPlaceholder('Select a channel for notifications')
            .setChannelTypes([ChannelType.GuildText, ChannelType.GuildAnnouncement]);

        const removeButton = new ButtonBuilder()
            .setCustomId('remove-main-channel')
            .setLabel('Remove Channel')
            .setStyle(ButtonStyle.Danger)
            .setDisabled(!notification || !notification.webhookUrl);

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const channelRow = new ActionRowBuilder().addComponents(channelSelect);
        const buttonRow = new ActionRowBuilder().addComponents(removeButton, backButton);

        const embed = new EmbedBuilder()
            .setColor('#f1fbd2')
            .setTitle('Set Notification Channel')
            .setDescription('Select a channel where stock notifications will be sent.');

        if (notification && notification.webhookUrl) {
            embed.addFields({ name: 'Current Status', value: '✅ Webhook configured', inline: true });
        }

        await interaction.update({
            embeds: [embed],
            components: [channelRow, buttonRow]
        });
    } else if (interaction.customId === 'channel-select') {
        const channel = interaction.channels.first();
    
        const botMember = interaction.guild.members.me;
        const botPermissions = channel.permissionsFor(botMember);
    
        if (!botPermissions || !botPermissions.has('SendMessages') || !botPermissions.has('ManageWebhooks')) {
            const backButton = new ButtonBuilder()
                .setCustomId('main-menu')
                .setLabel('Back')
                .setStyle(ButtonStyle.Secondary);

            const backRow = new ActionRowBuilder().addComponents(backButton);

            const missingPerms = [];
            if (!botPermissions || !botPermissions.has('SendMessages')) missingPerms.push('Send Messages');
            if (!botPermissions || !botPermissions.has('ManageWebhooks')) missingPerms.push('Manage Webhooks');

            const errorEmbed = new EmbedBuilder()
                .setColor('#ff4d4d')
                .setTitle('Permission Error')
                .setDescription(`I don't have the following permissions in ${channel}: ${missingPerms.join(', ')}. Please choose another channel or update my permissions.`);

            await interaction.update({
                embeds: [errorEmbed],
                components: [backRow]
            });

            return;
        }
    
        // Get existing config to check for old webhook
        const existingConfig = await StockNotification.findOne({ guildId: interaction.guildId });

        // Delete old webhook if it exists
        if (existingConfig?.webhookUrl) {
            await deleteWebhook(existingConfig.webhookUrl);
        }

        // Create new webhook
        const webhookUrl = await createWebhook(channel, getWebhookName('seeds'), getWebhookAvatar());

        if (!webhookUrl) {
            const backButton = new ButtonBuilder()
                .setCustomId('main-menu')
                .setLabel('Back')
                .setStyle(ButtonStyle.Secondary);

            const backRow = new ActionRowBuilder().addComponents(backButton);

            const errorEmbed = new EmbedBuilder()
                .setColor('#ff4d4d')
                .setTitle('Webhook Creation Failed')
                .setDescription(`Failed to create webhook in ${channel}. Please try again or contact support.`);

            await interaction.update({
                embeds: [errorEmbed],
                components: [backRow]
            });

            return;
        }

        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            {
                guildId: interaction.guildId,
                webhookUrl: webhookUrl
            },
            { upsert: true }
        );

        confirmationEmbed = new EmbedBuilder()
            .setTitle("Seed/Gear Stock")
            .setDescription("This channel has been configured for seed/gear stock updates. Please wait until the next restock for the first post. Seeds and gear restock every 5 minutes.")
            .setThumbnail("https://i.imgur.com/xPnNwfi.png")
            .setColor("#f1fbd2")

        try {
            channel.send({ embeds: [confirmationEmbed] });
        } catch (error) {

        }
    
        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
    
        const backRow = new ActionRowBuilder().addComponents(backButton);
    
        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Channel Set')
                .setDescription(`Notifications will now be sent to ${channel}`)],
            components: [backRow]
        });
    } else if (interaction.customId === 'remove-main-channel') {
        // Get existing config to delete webhook
        const existingConfig = await StockNotification.findOne({ guildId: interaction.guildId });

        // Delete webhook if it exists
        if (existingConfig?.webhookUrl) {
            await deleteWebhook(existingConfig.webhookUrl);
        }

        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            { $unset: { webhookUrl: 1 } }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Channel Removed')
                .setDescription('Main notification channel has been removed.')],
            components: [backRow]
        });
    } else if (interaction.customId === 'eggChannelConfigBtn') {
        const channelSelect = new ChannelSelectMenuBuilder()
            .setCustomId('egg-channel-select')
            .setPlaceholder('Select a channel for egg notifications')
            .setChannelTypes([ChannelType.GuildText, ChannelType.GuildAnnouncement]);

        const removeButton = new ButtonBuilder()
            .setCustomId('remove-egg-channel')
            .setLabel('Remove Channel')
            .setStyle(ButtonStyle.Danger)
            .setDisabled(!notification || !notification.eggWebhookUrl);

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const channelRow = new ActionRowBuilder().addComponents(channelSelect);
        const buttonRow = new ActionRowBuilder().addComponents(removeButton, backButton);

        const embed = new EmbedBuilder()
            .setColor('#f1fbd2')
            .setTitle('Set Egg Notification Channel')
            .setDescription('Select a channel where egg restock notifications will be sent.');

        if (notification && notification.eggWebhookUrl) {
            embed.addFields({ name: 'Current Status', value: '✅ Webhook configured', inline: true });
        }

        await interaction.update({
            embeds: [embed],
            components: [channelRow, buttonRow]
        });
    } else if (interaction.customId === 'egg-channel-select') {
        const channel = interaction.channels.first();
        const botMember = interaction.guild.members.me;
        const botPermissions = channel.permissionsFor(botMember);
        if (!botPermissions || !botPermissions.has('SendMessages') || !botPermissions.has('ManageWebhooks')) {
            const backButton = new ButtonBuilder()
                .setCustomId('main-menu')
                .setLabel('Back')
                .setStyle(ButtonStyle.Secondary);
            const backRow = new ActionRowBuilder().addComponents(backButton);

            const missingPerms = [];
            if (!botPermissions || !botPermissions.has('SendMessages')) missingPerms.push('Send Messages');
            if (!botPermissions || !botPermissions.has('ManageWebhooks')) missingPerms.push('Manage Webhooks');

            const errorEmbed = new EmbedBuilder()
                .setColor('#ff4d4d')
                .setTitle('Permission Error')
                .setDescription(`I don't have the following permissions in ${channel}: ${missingPerms.join(', ')}. Please choose another channel or update my permissions.`);
            await interaction.update({
                embeds: [errorEmbed],
                components: [backRow]
            });
            return;
        }

        // Get existing config to check for old webhook
        const existingConfig = await StockNotification.findOne({ guildId: interaction.guildId });

        // Delete old webhook if it exists
        if (existingConfig?.eggWebhookUrl) {
            await deleteWebhook(existingConfig.eggWebhookUrl);
        }

        // Create new webhook
        const webhookUrl = await createWebhook(channel, getWebhookName('eggs'), getWebhookAvatar());

        if (!webhookUrl) {
            const backButton = new ButtonBuilder()
                .setCustomId('main-menu')
                .setLabel('Back')
                .setStyle(ButtonStyle.Secondary);
            const backRow = new ActionRowBuilder().addComponents(backButton);
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff4d4d')
                .setTitle('Webhook Creation Failed')
                .setDescription(`Failed to create webhook in ${channel}. Please try again or contact support.`);
            await interaction.update({
                embeds: [errorEmbed],
                components: [backRow]
            });
            return;
        }

        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            { $set: { eggWebhookUrl: webhookUrl } },
            { upsert: true }
        );
        
        confirmationEmbed = new EmbedBuilder()
            .setTitle("Egg Stock")
            .setDescription("This channel has been configured for egg stock updates. Please wait until the next restock for the first post. Eggs restock every 30 minutes.")
            .setThumbnail("https://i.imgur.com/xPnNwfi.png")
            .setColor("#f1fbd2")

        try {
            channel.send({ embeds: [confirmationEmbed] });
        } catch (error) {
            
        }

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);
        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Egg Channel Set')
                .setDescription(`Egg restock notifications will now be sent to ${channel}`)],
            components: [backRow]
        });
    } else if (interaction.customId === 'remove-egg-channel') {
        // Get existing config to delete webhook
        const existingConfig = await StockNotification.findOne({ guildId: interaction.guildId });

        // Delete webhook if it exists
        if (existingConfig?.eggWebhookUrl) {
            await deleteWebhook(existingConfig.eggWebhookUrl);
        }

        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            { $unset: { eggWebhookUrl: 1 } }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Egg Channel Removed')
                .setDescription('Egg notification channel has been removed.')],
            components: [backRow]
        });
    } else if (interaction.customId === 'seedsGearRoleConfigBtn') {
        const seedRoleButton = new ButtonBuilder()
            .setCustomId('rolesConfigBtn')
            .setLabel('Seed Role Pings')
            .setStyle(ButtonStyle.Secondary);
        const gearRoleButton = new ButtonBuilder()
            .setCustomId('gearRolesConfigBtn')
            .setLabel('Gear Role Pings')
            .setStyle(ButtonStyle.Secondary);
        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const roleRow = new ActionRowBuilder().addComponents(seedRoleButton, gearRoleButton, backButton);
        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Role Configuration')
                .setDescription('Select which type of role pings you want to configure.')],
            components: [roleRow]
        });
        return;
    } else if (interaction.customId === 'rolesConfigBtn') {
        if (!notification) {
            await interaction.reply({
                content: 'Please set up a notification channel first!',
                flags: MessageFlags.Ephemeral
            });
            return;
        }
        await setupSeed(interaction, 0);
    } else if (interaction.customId === 'gearRolesConfigBtn') {
        if (!notification) {
            await interaction.reply({
                content: 'Please set up a notification channel first!',
                flags: MessageFlags.Ephemeral
            });
            return;
        }
        await setupGear(interaction, 0);
    } else if (interaction.customId === 'eggRolesConfigBtn') {
        if (!notification || !notification.eggWebhookUrl) {
            await interaction.reply({
                content: 'Please set up an egg notification channel first!',
                flags: MessageFlags.Ephemeral
            });
            return;
        }
        await setupEgg(interaction, 0);
    } else if (interaction.customId === 'weatherRolesConfigBtn') {
        if (!notification || !notification.weatherWebhookUrl) {
            await interaction.reply({
                content: 'Please set up a weather notification channel first!',
                flags: MessageFlags.Ephemeral
            });
            return;
        }
        await setupWeather(interaction, 0);
    } else if (interaction.customId === 'weatherChannelConfigBtn') {
        const channelSelect = new ChannelSelectMenuBuilder()
            .setCustomId('weather-channel-select')
            .setPlaceholder('Select a channel for weather notifications')
            .setChannelTypes([ChannelType.GuildText, ChannelType.GuildAnnouncement]);

        const removeButton = new ButtonBuilder()
            .setCustomId('remove-weather-channel')
            .setLabel('Remove Channel')
            .setStyle(ButtonStyle.Danger)
            .setDisabled(!notification || !notification.weatherWebhookUrl);

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const channelRow = new ActionRowBuilder().addComponents(channelSelect);
        const buttonRow = new ActionRowBuilder().addComponents(removeButton, backButton);

        const embed = new EmbedBuilder()
            .setColor('#f1fbd2')
            .setTitle('Set Weather Notification Channel')
            .setDescription('Select a channel where weather notifications will be sent.');

        if (notification && notification.weatherWebhookUrl) {
            embed.addFields({ name: 'Current Status', value: '✅ Webhook configured', inline: true });
        }

        await interaction.update({
            embeds: [embed],
            components: [channelRow, buttonRow]
        });
        return;
    } else if (interaction.customId === 'weather-channel-select') {
        const channel = interaction.channels.first();
        const botMember = interaction.guild.members.me;
        const botPermissions = channel.permissionsFor(botMember);
        if (!botPermissions || !botPermissions.has('SendMessages') || !botPermissions.has('ManageWebhooks')) {
            const backButton = new ButtonBuilder()
                .setCustomId('main-menu')
                .setLabel('Back')
                .setStyle(ButtonStyle.Secondary);
            const backRow = new ActionRowBuilder().addComponents(backButton);

            const missingPerms = [];
            if (!botPermissions || !botPermissions.has('SendMessages')) missingPerms.push('Send Messages');
            if (!botPermissions || !botPermissions.has('ManageWebhooks')) missingPerms.push('Manage Webhooks');

            const errorEmbed = new EmbedBuilder()
                .setColor('#ff4d4d')
                .setTitle('Permission Error')
                .setDescription(`I don't have the following permissions in ${channel}: ${missingPerms.join(', ')}. Please choose another channel or update my permissions.`);
            await interaction.update({
                embeds: [errorEmbed],
                components: [backRow]
            });
            return;
        }

        // Get existing config to check for old webhook
        const existingConfig = await StockNotification.findOne({ guildId: interaction.guildId });

        // Delete old webhook if it exists
        if (existingConfig?.weatherWebhookUrl) {
            await deleteWebhook(existingConfig.weatherWebhookUrl);
        }

        // Create new webhook
        const webhookUrl = await createWebhook(channel, getWebhookName('weather'));

        if (!webhookUrl) {
            const backButton = new ButtonBuilder()
                .setCustomId('main-menu')
                .setLabel('Back')
                .setStyle(ButtonStyle.Secondary);
            const backRow = new ActionRowBuilder().addComponents(backButton);
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff4d4d')
                .setTitle('Webhook Creation Failed')
                .setDescription(`Failed to create webhook in ${channel}. Please try again or contact support.`);
            await interaction.update({
                embeds: [errorEmbed],
                components: [backRow]
            });
            return;
        }

        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            { $set: { weatherWebhookUrl: webhookUrl } },
            { upsert: true }
        );

        confirmationEmbed = new EmbedBuilder()
            .setTitle("Weather Updates")
            .setDescription("This channel has been configured for weather updates. Please wait until the next weather event for the first post.")
            .setThumbnail("https://i.imgur.com/xPnNwfi.png")
            .setColor("#f1fbd2")

        try {
            channel.send({ embeds: [confirmationEmbed] });
        } catch (error) {
            
        }

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Weather Channel Set')
                .setDescription(`Weather notifications will now be sent to ${channel}`)],
            components: [backRow]
        });

        return;
    } else if (interaction.customId === 'remove-weather-channel') {
        // Get existing config to delete webhook
        const existingConfig = await StockNotification.findOne({ guildId: interaction.guildId });

        // Delete webhook if it exists
        if (existingConfig?.weatherWebhookUrl) {
            await deleteWebhook(existingConfig.weatherWebhookUrl);
        }

        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            { $unset: { weatherWebhookUrl: 1 } }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Weather Channel Removed')
                .setDescription('Weather notification channel has been removed.')],
            components: [backRow]
        });
        return;
    } else if (interaction.customId === 'eventChannelConfigBtn') {
        const channelSelect = new ChannelSelectMenuBuilder()
            .setCustomId('event-channel-select')
            .setPlaceholder('Select a channel for event shop notifications')
            .setChannelTypes([ChannelType.GuildText, ChannelType.GuildAnnouncement]);

        const removeButton = new ButtonBuilder()
            .setCustomId('remove-event-channel')
            .setLabel('Remove Channel')
            .setStyle(ButtonStyle.Danger)
            .setDisabled(!notification || !notification.eventWebhookUrl);

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const channelRow = new ActionRowBuilder().addComponents(channelSelect);
        const buttonRow = new ActionRowBuilder().addComponents(removeButton, backButton);

        const embed = new EmbedBuilder()
            .setColor('#f1fbd2')
            .setTitle('Set Event Shop Notification Channel')
            .setDescription('Select a channel where event shop notifications will be sent.');

        if (notification && notification.eventWebhookUrl) {
            embed.addFields({ name: 'Current Status', value: '✅ Webhook configured', inline: true });
        }

        await interaction.update({
            embeds: [embed],
            components: [channelRow, buttonRow]
        });
    } else if (interaction.customId === 'event-channel-select') {
        const channel = interaction.channels.first();
        const botMember = interaction.guild.members.me;
        const botPermissions = channel.permissionsFor(botMember);
        if (!botPermissions || !botPermissions.has('SendMessages') || !botPermissions.has('ManageWebhooks')) {
            const backButton = new ButtonBuilder()
                .setCustomId('main-menu')
                .setLabel('Back')
                .setStyle(ButtonStyle.Secondary);
            const backRow = new ActionRowBuilder().addComponents(backButton);

            const missingPerms = [];
            if (!botPermissions || !botPermissions.has('SendMessages')) missingPerms.push('Send Messages');
            if (!botPermissions || !botPermissions.has('ManageWebhooks')) missingPerms.push('Manage Webhooks');

            const errorEmbed = new EmbedBuilder()
                .setColor('#ff4d4d')
                .setTitle('Permission Error')
                .setDescription(`I don't have the following permissions in ${channel}: ${missingPerms.join(', ')}. Please choose another channel or update my permissions.`);
            await interaction.update({
                embeds: [errorEmbed],
                components: [backRow]
            });
            return;
        }

        // Get existing config to check for old webhook
        const existingConfig = await StockNotification.findOne({ guildId: interaction.guildId });

        // Delete old webhook if it exists
        if (existingConfig?.eventWebhookUrl) {
            await deleteWebhook(existingConfig.eventWebhookUrl);
        }

        // Create new webhook
        const webhookUrl = await createWebhook(channel, getWebhookName('events'), getWebhookAvatar());

        if (!webhookUrl) {
            const backButton = new ButtonBuilder()
                .setCustomId('main-menu')
                .setLabel('Back')
                .setStyle(ButtonStyle.Secondary);
            const backRow = new ActionRowBuilder().addComponents(backButton);
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff4d4d')
                .setTitle('Webhook Creation Failed')
                .setDescription(`Failed to create webhook in ${channel}. Please try again or contact support.`);
            await interaction.update({
                embeds: [errorEmbed],
                components: [backRow]
            });
            return;
        }

        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            { $set: { eventWebhookUrl: webhookUrl } },
            { upsert: true }
        );
        
        confirmationEmbed = new EmbedBuilder()
            .setTitle('Event Shop Stock')
            .setDescription('This channel has been configured for event shop stock updates. Please wait until the next restock for the first post. Event shop restocks every 30 minutes.')
            .setThumbnail('https://i.imgur.com/xPnNwfi.png')
            .setColor('#f1fbd2');

        channel.send({ embeds: [confirmationEmbed] });

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const backRow = new ActionRowBuilder().addComponents(backButton);
        
        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Event Shop Channel Set')
                .setDescription(`Event shop notifications will now be sent to ${channel}`)],
            components: [backRow]
        });
    } else if (interaction.customId === 'remove-event-channel') {
        // Get existing config to delete webhook
        const existingConfig = await StockNotification.findOne({ guildId: interaction.guildId });

        // Delete webhook if it exists
        if (existingConfig?.eventWebhookUrl) {
            await deleteWebhook(existingConfig.eventWebhookUrl);
        }

        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            { $unset: { eventWebhookUrl: 1 } }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Event Channel Removed')
                .setDescription('Event shop notification channel has been removed.')],
            components: [backRow]
        });
    } else if (interaction.customId === 'eventRolesConfigBtn') {
        if (!notification || !notification.eventWebhookUrl) {
            await interaction.reply({
                content: 'Please set up an event shop notification channel first!',
                flags: MessageFlags.Ephemeral
            });
            return;
        }
        await setupEvent(interaction, 0);
    } else if (interaction.customId.startsWith('role-select')) {
        const index = parseInt(interaction.customId.split('-')[2]);
        interaction.deferUpdate();
        const availableFruits = getAvailableFruits(); // Get fresh data
        const seed = availableFruits[index];
        const roleId = interaction.values[0];

        // Update seed role using new single role method
        await SeedRoleConfig.updateSingleRole(interaction.guildId, 'seedRoles', seed.name, roleId);
    } else if (interaction.customId.startsWith('remove-seed-role-')) {
        const index = parseInt(interaction.customId.split('-')[3]);
        const availableFruits = getAvailableFruits(); // Get fresh data
        const seed = availableFruits[index];
        // Remove seed role using new single role method
        await SeedRoleConfig.deleteSingleRole(interaction.guildId, 'seedRoles', seed.name);
        await setupSeed(interaction, index);
    } else if (interaction.customId.startsWith('gear-role-select')) {
        const index = parseInt(interaction.customId.split('-')[3]);
        interaction.deferUpdate();
        const availableGear = getAvailableGear(); // Get fresh data
        const gear = availableGear[index];
        const roleId = interaction.values[0];

        // Update gear role using new single role method
        await SeedRoleConfig.updateSingleRole(interaction.guildId, 'gearRoles', gear.name, roleId);
    } else if (interaction.customId.startsWith('remove-gear-role-')) {
        const index = parseInt(interaction.customId.split('-')[3]);
        const availableGear = getAvailableGear(); // Get fresh data
        const gear = availableGear[index];
        // Remove gear role using new single role method
        await SeedRoleConfig.deleteSingleRole(interaction.guildId, 'gearRoles', gear.name);
        await setupGear(interaction, index);
    } else if (interaction.customId.startsWith('egg-role-select')) {
        const index = parseInt(interaction.customId.split('-')[3]);
        interaction.deferUpdate();
        const availableEggs = getAvailableEggs(); // Get fresh data
        const egg = availableEggs[index];
        const roleId = interaction.values[0];
        // Update egg role using new single role method
        await SeedRoleConfig.updateSingleRole(interaction.guildId, 'eggRoles', egg.name, roleId);
    } else if (interaction.customId.startsWith('remove-egg-role-')) {
        const index = parseInt(interaction.customId.split('-')[3]);
        const availableEggs = getAvailableEggs(); // Get fresh data
        const egg = availableEggs[index];
        // Remove egg role using new single role method
        await SeedRoleConfig.deleteSingleRole(interaction.guildId, 'eggRoles', egg.name);
        await setupEgg(interaction, index);
    } else if (interaction.customId.startsWith('weather-role-select')) {
        const index = parseInt(interaction.customId.split('-')[3]);
        interaction.deferUpdate();
        const availableWeather = getAvailableWeather(); // Get fresh data
        const weather = availableWeather[index];
        const roleId = interaction.values[0];
        // Update weather role using new single role method
        await SeedRoleConfig.updateSingleRole(interaction.guildId, 'weatherRoles', weather.apiName, roleId);
    } else if (interaction.customId.startsWith('remove-weather-role-')) {
        const index = parseInt(interaction.customId.split('-')[3]);
        const availableWeather = getAvailableWeather(); // Get fresh data
        const weather = availableWeather[index];
        // Remove weather role using new single role method
        await SeedRoleConfig.deleteSingleRole(interaction.guildId, 'weatherRoles', weather.apiName);
        await setupWeather(interaction, index);
    } else if (interaction.customId.startsWith('nextRoleMenu')) {
        const index = parseInt(interaction.customId.split('-')[1]);
        await setupSeed(interaction, index + 1);
    } else if (interaction.customId.startsWith('nextGearRoleMenu')) {
        const index = parseInt(interaction.customId.split('-')[1]);
        await setupGear(interaction, index + 1);
    } else if (interaction.customId.startsWith('nextEggRoleMenu')) {
        const index = parseInt(interaction.customId.split('-')[1]);
        await setupEgg(interaction, index + 1);
    } else if (interaction.customId.startsWith('nextWeatherRoleMenu')) {
        const index = parseInt(interaction.customId.split('-')[1]);
        await setupWeather(interaction, index + 1);
    } else if (interaction.customId.startsWith('backRoleMenu')) {
        const index = parseInt(interaction.customId.split('-')[1]);
        await setupSeed(interaction, index - 1);
    } else if (interaction.customId.startsWith('backGearRoleMenu')) {
        const index = parseInt(interaction.customId.split('-')[1]);
        await setupGear(interaction, index - 1);
    } else if (interaction.customId.startsWith('backEggRoleMenu')) {
        const index = parseInt(interaction.customId.split('-')[1]);
        await setupEgg(interaction, index - 1);
    } else if (interaction.customId.startsWith('backWeatherRoleMenu')) {
        const index = parseInt(interaction.customId.split('-')[1]);
        await setupWeather(interaction, index - 1);
    } else if (interaction.customId === 'main-menu') {
        await interaction.update({
            embeds: [mainEmbed],
            components: [mainRow]
        });
    } else if (interaction.customId === 'removeConfigBtn') {
        const confirmButton = new ButtonBuilder()
            .setCustomId('confirm-remove')
            .setLabel('Confirm')
            .setStyle(ButtonStyle.Danger);

        const cancelButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Cancel')
            .setStyle(ButtonStyle.Secondary);

        const confirmRow = new ActionRowBuilder().addComponents(confirmButton, cancelButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Confirm Removal')
                .setDescription('Are you sure you want to remove all stock notification settings? This cannot be undone.')],
            components: [confirmRow]
        });
    } else if (interaction.customId === 'confirm-remove') {
        const existingConfig = await StockNotification.findOne({ guildId: interaction.guildId });

        if (existingConfig?.webhookUrl) {
            await deleteWebhook(existingConfig.webhookUrl);
        }
        if (existingConfig?.eggWebhookUrl) {
            await deleteWebhook(existingConfig.eggWebhookUrl);
        }
        if (existingConfig?.weatherWebhookUrl) {
            await deleteWebhook(existingConfig.weatherWebhookUrl);
        }
        if (existingConfig?.eventWebhookUrl) {
            await deleteWebhook(existingConfig.eventWebhookUrl);
        }
        if (existingConfig?.cosmeticWebhookUrl) {
            await deleteWebhook(existingConfig.cosmeticWebhookUrl);
        }
        if (existingConfig?.merchantWebhookUrl) {
            await deleteWebhook(existingConfig.merchantWebhookUrl);
        }

        await StockNotification.findOneAndDelete({ guildId: interaction.guildId });
        await SeedRoleConfig.findOneAndDelete({ guildId: interaction.guildId });
        await AutoReactionConfig?.findOneAndDelete({ guildId: interaction.guildId });

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Settings Removed')
                .setDescription('All stock notification settings have been removed.')],
            components: [backRow]
        });
    } else if (interaction.customId.startsWith('event-role-select')) {
        const index = parseInt(interaction.customId.split('-')[3]);
        interaction.deferUpdate();
        const availableEvents = getAvailableEvents(); // Get fresh data
        const eventItem = availableEvents[index];
        const roleId = interaction.values[0];
        // Update event role using new single role method
        await SeedRoleConfig.updateSingleRole(interaction.guildId, 'eventRoles', eventItem.name, roleId);
    } else if (interaction.customId.startsWith('remove-event-role-')) {
        const index = parseInt(interaction.customId.split('-')[3]);
        const availableEvents = getAvailableEvents(); // Get fresh data
        const eventItem = availableEvents[index];
        // Remove event role using new single role method
        await SeedRoleConfig.deleteSingleRole(interaction.guildId, 'eventRoles', eventItem.name);
        await setupEvent(interaction, index);
    } else if (interaction.customId.startsWith('nextEventRoleMenu')) {
        const index = parseInt(interaction.customId.split('-')[1]);
        await setupEvent(interaction, index + 1);
    } else if (interaction.customId.startsWith('backEventRoleMenu')) {
        const index = parseInt(interaction.customId.split('-')[1]);
        await setupEvent(interaction, index - 1);
    } else if (interaction.customId.startsWith('nextMerchantRoleMenu')) {
        const index = parseInt(interaction.customId.split('-')[1]);
        await setupMerchant(interaction, index + 1);
    } else if (interaction.customId.startsWith('backMerchantRoleMenu')) {
        const index = parseInt(interaction.customId.split('-')[1]);
        await setupMerchant(interaction, index - 1);
    } else if (interaction.customId === 'cosmetic-role-select') {
        const roleId = interaction.values[0];
        // Update cosmetic role using Prisma
        await SeedRoleConfig.findOneAndUpdate(
            { guildId: interaction.guildId },
            { cosmeticRole: roleId },
            { upsert: true }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Cosmetic Role Set')
                .setDescription(`Cosmetic notifications will now ping <@&${roleId}>.`)],
            components: [backRow]
        });
    } else if (interaction.customId === 'remove-cosmetic-role') {
        // Remove cosmetic role using Prisma
        await SeedRoleConfig.findOneAndUpdate(
            { guildId: interaction.guildId },
            { cosmeticRole: null },
            { upsert: true }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Cosmetic Role Removed')
                .setDescription('Cosmetic role ping has been removed.')],
            components: [backRow]
        });
    } else if (interaction.customId === 'seedsGearAutoReactionBtn') {
        const autoReactionConfig = await AutoReactionConfig.findOne({ guildId: interaction.guildId }) || { seedsGearAutoReaction: false, eggAutoReaction: false, eventAutoReaction: false, cosmeticAutoReaction: false, merchantAutoReaction: false };

        const toggleButton = new ButtonBuilder()
            .setCustomId('toggle-seedsgear-auto-reaction')
            .setLabel(`${autoReactionConfig.seedsGearAutoReaction ? 'Disable' : 'Enable'} Auto-Reactions`)
            .setStyle(autoReactionConfig.seedsGearAutoReaction ? ButtonStyle.Danger : ButtonStyle.Success);

        const backButton = new ButtonBuilder()
            .setCustomId('seedsGearNavBtn')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const row = new ActionRowBuilder().addComponents(toggleButton, backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Seeds/Gear Auto-Reactions')
                .setDescription('Enable or disable automatic 👍 and 👎 reactions on seeds and gear stock notifications.')
                .addFields([
                    { name: 'Current Status', value: autoReactionConfig.seedsGearAutoReaction ? '✅ Enabled' : '❌ Disabled', inline: true }
                ])],
            components: [row]
        });
    } else if (interaction.customId === 'eggAutoReactionBtn') {
        const autoReactionConfig = await AutoReactionConfig.findOne({ guildId: interaction.guildId }) || { seedsGearAutoReaction: false, eggAutoReaction: false, eventAutoReaction: false, cosmeticAutoReaction: false, merchantAutoReaction: false };

        const toggleButton = new ButtonBuilder()
            .setCustomId('toggle-egg-auto-reaction')
            .setLabel(`${autoReactionConfig.eggAutoReaction ? 'Disable' : 'Enable'} Auto-Reactions`)
            .setStyle(autoReactionConfig.eggAutoReaction ? ButtonStyle.Danger : ButtonStyle.Success);

        const backButton = new ButtonBuilder()
            .setCustomId('eggsNavBtn')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const row = new ActionRowBuilder().addComponents(toggleButton, backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Egg Auto-Reactions')
                .setDescription('Enable or disable automatic 👍 and 👎 reactions on egg stock notifications.')
                .addFields([
                    { name: 'Current Status', value: autoReactionConfig.eggAutoReaction ? '✅ Enabled' : '❌ Disabled', inline: true }
                ])],
            components: [row]
        });
    } else if (interaction.customId === 'eventAutoReactionBtn') {
        const autoReactionConfig = await AutoReactionConfig.findOne({ guildId: interaction.guildId }) || { seedsGearAutoReaction: false, eggAutoReaction: false, eventAutoReaction: false, cosmeticAutoReaction: false, merchantAutoReaction: false };

        const toggleButton = new ButtonBuilder()
            .setCustomId('toggle-event-auto-reaction')
            .setLabel(`${autoReactionConfig.eventAutoReaction ? 'Disable' : 'Enable'} Auto-Reactions`)
            .setStyle(autoReactionConfig.eventAutoReaction ? ButtonStyle.Danger : ButtonStyle.Success);

        const backButton = new ButtonBuilder()
            .setCustomId('eventNavBtn')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const row = new ActionRowBuilder().addComponents(toggleButton, backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Event Shop Auto-Reactions')
                .setDescription('Enable or disable automatic 👍 and 👎 reactions on event shop notifications.')
                .addFields([
                    { name: 'Current Status', value: autoReactionConfig.eventAutoReaction ? '✅ Enabled' : '❌ Disabled', inline: true }
                ])],
            components: [row]
        });
    } else if (interaction.customId === 'cosmeticAutoReactionBtn') {
        const autoReactionConfig = await AutoReactionConfig.findOne({ guildId: interaction.guildId }) || { seedsGearAutoReaction: false, eggAutoReaction: false, eventAutoReaction: false, cosmeticAutoReaction: false, merchantAutoReaction: false };

        const toggleButton = new ButtonBuilder()
            .setCustomId('toggle-cosmetic-auto-reaction')
            .setLabel(`${autoReactionConfig.cosmeticAutoReaction ? 'Disable' : 'Enable'} Auto-Reactions`)
            .setStyle(autoReactionConfig.cosmeticAutoReaction ? ButtonStyle.Danger : ButtonStyle.Success);

        const backButton = new ButtonBuilder()
            .setCustomId('cosmeticNavBtn')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const row = new ActionRowBuilder().addComponents(toggleButton, backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Cosmetic Auto-Reactions')
                .setDescription('Enable or disable automatic 👍 and 👎 reactions on cosmetic notifications.')
                .addFields([
                    { name: 'Current Status', value: autoReactionConfig.cosmeticAutoReaction ? '✅ Enabled' : '❌ Disabled', inline: true }
                ])],
            components: [row]
        });
    } else if (interaction.customId === 'merchantAutoReactionBtn') {
        const autoReactionConfig = await AutoReactionConfig.findOne({ guildId: interaction.guildId }) || { seedsGearAutoReaction: false, eggAutoReaction: false, eventAutoReaction: false, cosmeticAutoReaction: false, merchantAutoReaction: false };

        const toggleButton = new ButtonBuilder()
            .setCustomId('toggle-merchant-auto-reaction')
            .setLabel(`${autoReactionConfig.merchantAutoReaction ? 'Disable' : 'Enable'} Auto-Reactions`)
            .setStyle(autoReactionConfig.merchantAutoReaction ? ButtonStyle.Danger : ButtonStyle.Success);

        const backButton = new ButtonBuilder()
            .setCustomId('merchantNavBtn')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);

        const row = new ActionRowBuilder().addComponents(toggleButton, backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Merchant Auto-Reactions')
                .setDescription('Enable or disable automatic 👍 and 👎 reactions on merchant notifications.')
                .addFields([
                    { name: 'Current Status', value: autoReactionConfig.merchantAutoReaction ? '✅ Enabled' : '❌ Disabled', inline: true }
                ])],
            components: [row]
        });
    } else if (interaction.customId === 'toggle-seedsgear-auto-reaction') {
        // Toggle seeds/gear auto reaction using Prisma
        const currentConfig = await AutoReactionConfig.findOne({ guildId: interaction.guildId });
        const newValue = !currentConfig?.seedsGearAutoReaction;

        await AutoReactionConfig.findOneAndUpdate(
            { guildId: interaction.guildId },
            { seedsGearAutoReaction: newValue },
            { upsert: true }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('seedsGearAutoReactionBtn')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Seeds/Gear Auto-Reactions Updated')
                .setDescription(`Seeds and gear auto-reactions are now ${newValue ? 'enabled' : 'disabled'}.`)],
            components: [backRow]
        });
    } else if (interaction.customId === 'toggle-egg-auto-reaction') {
        // Toggle egg auto reaction using Prisma
        const currentConfig = await AutoReactionConfig.findOne({ guildId: interaction.guildId });
        const newValue = !currentConfig?.eggAutoReaction;

        await AutoReactionConfig.findOneAndUpdate(
            { guildId: interaction.guildId },
            { eggAutoReaction: newValue },
            { upsert: true }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('eggAutoReactionBtn')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Egg Auto-Reactions Updated')
                .setDescription(`Egg auto-reactions are now ${newValue ? 'enabled' : 'disabled'}.`)],
            components: [backRow]
        });
    } else if (interaction.customId === 'toggle-event-auto-reaction') {
        // Toggle event auto reaction using Prisma
        const currentConfig = await AutoReactionConfig.findOne({ guildId: interaction.guildId });
        const newValue = !currentConfig?.eventAutoReaction;

        await AutoReactionConfig.findOneAndUpdate(
            { guildId: interaction.guildId },
            { eventAutoReaction: newValue },
            { upsert: true }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('eventAutoReactionBtn')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Event Auto-Reactions Updated')
                .setDescription(`Event shop auto-reactions are now ${newValue ? 'enabled' : 'disabled'}.`)],
            components: [backRow]
        });
    } else if (interaction.customId === 'toggle-cosmetic-auto-reaction') {
        // Toggle cosmetic auto reaction using Prisma
        const currentConfig = await AutoReactionConfig.findOne({ guildId: interaction.guildId });
        const newValue = !currentConfig?.cosmeticAutoReaction;

        await AutoReactionConfig.findOneAndUpdate(
            { guildId: interaction.guildId },
            { cosmeticAutoReaction: newValue },
            { upsert: true }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('cosmeticAutoReactionBtn')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Cosmetic Auto-Reactions Updated')
                .setDescription(`Cosmetic auto-reactions are now ${newValue ? 'enabled' : 'disabled'}.`)],
            components: [backRow]
        });
    } else if (interaction.customId === 'toggle-merchant-auto-reaction') {
        // Toggle merchant auto reaction using Prisma
        const currentConfig = await AutoReactionConfig.findOne({ guildId: interaction.guildId });
        const newValue = !currentConfig?.merchantAutoReaction;

        await AutoReactionConfig.findOneAndUpdate(
            { guildId: interaction.guildId },
            { merchantAutoReaction: newValue },
            { upsert: true }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('merchantAutoReactionBtn')
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Merchant Auto-Reactions Updated')
                .setDescription(`Merchant auto-reactions are now ${newValue ? 'enabled' : 'disabled'}.`)],
            components: [backRow]
        });
    } else if (interaction.customId === 'remove-seedsgear-settings') {
        // Remove all seeds and gear settings for this guild
        const existingConfig = await SeedRoleConfig.findOne({ guildId: interaction.guildId });
        if (existingConfig) {
            // Clear seed and gear roles
            const updatedSeedRoles = new Map();
            const updatedGearRoles = new Map();

            await SeedRoleConfig.findOneAndUpdate(
                { guildId: interaction.guildId },
                {
                    seedRoles: updatedSeedRoles,
                    gearRoles: updatedGearRoles
                },
                { upsert: true }
            );
        }

        // Clear auto-reactions for seeds/gear
        await AutoReactionConfig.findOneAndUpdate(
            { guildId: interaction.guildId },
            { seedsGearAutoReaction: false },
            { upsert: true }
        );

        // Clear webhook URLs
        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            { webhookUrl: null },
            { upsert: true }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back to Main Menu')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Seeds/Gear Settings Removed')
                .setDescription('All seeds and gear notification settings have been removed for this server.')],
            components: [backRow]
        });
    } else if (interaction.customId === 'remove-eggs-settings') {
        // Remove all egg settings for this guild
        const existingConfig = await SeedRoleConfig.findOne({ guildId: interaction.guildId });
        if (existingConfig) {
            // Clear egg roles
            const updatedEggRoles = new Map();

            await SeedRoleConfig.findOneAndUpdate(
                { guildId: interaction.guildId },
                { eggRoles: updatedEggRoles },
                { upsert: true }
            );
        }

        // Clear auto-reactions for eggs
        await AutoReactionConfig.findOneAndUpdate(
            { guildId: interaction.guildId },
            { eggAutoReaction: false },
            { upsert: true }
        );

        // Clear webhook URLs
        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            { eggWebhookUrl: null },
            { upsert: true }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back to Main Menu')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Egg Settings Removed')
                .setDescription('All egg notification settings have been removed for this server.')],
            components: [backRow]
        });
    } else if (interaction.customId === 'remove-weather-settings') {
        // Remove all weather settings for this guild
        const existingConfig = await SeedRoleConfig.findOne({ guildId: interaction.guildId });
        if (existingConfig) {
            // Clear weather roles
            const updatedWeatherRoles = new Map();

            await SeedRoleConfig.findOneAndUpdate(
                { guildId: interaction.guildId },
                { weatherRoles: updatedWeatherRoles },
                { upsert: true }
            );
        }

        // Clear webhook URLs
        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            { weatherWebhookUrl: null },
            { upsert: true }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back to Main Menu')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Weather Settings Removed')
                .setDescription('All weather notification settings have been removed for this server.')],
            components: [backRow]
        });
    } else if (interaction.customId === 'remove-events-settings') {
        // Remove all event settings for this guild
        const existingConfig = await SeedRoleConfig.findOne({ guildId: interaction.guildId });
        if (existingConfig) {
            // Clear event roles
            const updatedEventRoles = new Map();

            await SeedRoleConfig.findOneAndUpdate(
                { guildId: interaction.guildId },
                { eventRoles: updatedEventRoles },
                { upsert: true }
            );
        }

        // Clear auto-reactions for events
        await AutoReactionConfig.findOneAndUpdate(
            { guildId: interaction.guildId },
            { eventAutoReaction: false },
            { upsert: true }
        );

        // Clear webhook URLs
        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            { eventWebhookUrl: null },
            { upsert: true }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back to Main Menu')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Event Settings Removed')
                .setDescription('All event shop notification settings have been removed for this server.')],
            components: [backRow]
        });
    } else if (interaction.customId === 'remove-cosmetics-settings') {
        // Remove all cosmetic settings for this guild
        await SeedRoleConfig.findOneAndUpdate(
            { guildId: interaction.guildId },
            { cosmeticRole: null },
            { upsert: true }
        );

        // Clear auto-reactions for cosmetics
        await AutoReactionConfig.findOneAndUpdate(
            { guildId: interaction.guildId },
            { cosmeticAutoReaction: false },
            { upsert: true }
        );

        // Clear webhook URLs
        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            { cosmeticWebhookUrl: null },
            { upsert: true }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back to Main Menu')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Cosmetic Settings Removed')
                .setDescription('All cosmetic notification settings have been removed for this server.')],
            components: [backRow]
        });
    } else if (interaction.customId === 'remove-merchant-settings') {
        // Remove all merchant settings for this guild
        const existingConfig = await SeedRoleConfig.findOne({ guildId: interaction.guildId });
        if (existingConfig) {
            // Clear merchant roles
            const updatedMerchantRoles = new Map();

            await SeedRoleConfig.findOneAndUpdate(
                { guildId: interaction.guildId },
                { merchantRoles: updatedMerchantRoles },
                { upsert: true }
            );
        }

        // Clear auto-reactions for merchant
        await AutoReactionConfig.findOneAndUpdate(
            { guildId: interaction.guildId },
            { merchantAutoReaction: false },
            { upsert: true }
        );

        // Clear webhook URLs
        await StockNotification.findOneAndUpdate(
            { guildId: interaction.guildId },
            { merchantWebhookUrl: null },
            { upsert: true }
        );

        const backButton = new ButtonBuilder()
            .setCustomId('main-menu')
            .setLabel('Back to Main Menu')
            .setStyle(ButtonStyle.Secondary);
        const backRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.update({
            embeds: [new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Merchant Settings Removed')
                .setDescription('All merchant notification settings have been removed for this server.')],
            components: [backRow]
        });
    }
};
