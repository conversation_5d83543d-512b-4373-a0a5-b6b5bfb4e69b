const { ApplicationCommandOptionType, EmbedBuilder, MessageFlags } = require('discord.js');
const fs = require('fs').promises;
const path = require('path');

const DEFAULT_ALL_ITEMS_PATH = process.env.ALL_ITEMS_PATH || path.join(__dirname, '..', 'data', 'allItems.json');
const FALLBACK_ALL_ITEMS_PATH = path.join(__dirname, '..', 'allItemsAlterable.json');
function getAllItemsPath() {
    try {
        require('fs').accessSync(DEFAULT_ALL_ITEMS_PATH);
        return DEFAULT_ALL_ITEMS_PATH;
    } catch {
        return FALLBACK_ALL_ITEMS_PATH;
    }
}

function formatNumber(num) {
    const fullNumber = num.toLocaleString();
    if (num >= 1000000000) {
        return `${(num / 1000000000).toFixed(1)}B`;
    } else if (num >= 1000000) {
        return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
        return `${(num / 1000).toFixed(1)}K`;
    }
    return fullNumber;
}

function formatLastSeen(lastSeen) {
    if (!lastSeen || lastSeen === '') {
        return 'Never seen in stock';
    }
    
    try {
        const date = new Date(lastSeen);
        const now = new Date();
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
        
        if (diffDays > 0) {
            return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
        } else if (diffHours > 0) {
            return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
        } else if (diffMinutes > 0) {
            return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
        } else {
            return 'Just now';
        }
    } catch (error) {
        return 'Unknown';
    }
}

module.exports = {
    data: {
        name: 'item',
        description: 'View the information of an item',
        options: [
            {
                name: 'item',
                description: 'The item to view information of',
                type: ApplicationCommandOptionType.String,
                required: true,
                autocomplete: true
            }
        ]
    },
 
    run: async ({ interaction, client, handler }) => {
        await interaction.deferReply();

        try {
            const itemName = interaction.options.getString('item');
            const filePath = getAllItemsPath();
            const itemsData = JSON.parse(await fs.readFile(filePath, 'utf8'));

            const item = itemsData.find(i => i.name === itemName);

            if (!item) {
                return interaction.editReply('Item not found.');
            }

            const embed = new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle(`${item.emoji || ''} ${item.name}`)
                .setThumbnail(item.image)
                .addFields([
                    { name: 'Category', value: item.category || 'N/A', inline: true },
                    { name: 'Rarity', value: item.rarity || 'N/A', inline: true },
                    { name: 'Stockable', value: item.stockable ? 'Yes' : 'No', inline: true },
                    { name: 'Added', value: new Date(item.createdAt).toLocaleDateString(), inline: true },
                    { name: 'Last Seen', value: formatLastSeen(item.lastSeen), inline: true }
                ])
                .setTimestamp();

            // Add category-specific information
            if (item.category === 'Pets') {
                embed.addFields([
                    { name: 'Pet Type', value: item.rarity || 'N/A', inline: true }
                ]);
            } else if (item.category === 'Eggs') {
                embed.addFields([
                    { name: 'Egg Type', value: item.rarity || 'N/A', inline: true }
                ]);
            } else if (item.category === 'Gears') {
                embed.addFields([
                    { name: 'Gear Type', value: item.rarity || 'N/A', inline: true }
                ]);
            } else if (item.category === 'Cosmetics') {
                embed.addFields([
                    { name: 'Cosmetic Type', value: item.rarity || 'N/A', inline: true }
                ]);
            } else if (item.category === 'Events') {
                embed.addFields([
                    { name: 'Event Type', value: item.rarity || 'N/A', inline: true }
                ]);
            }

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Error fetching item info:', error);
            await interaction.editReply('An error occurred while fetching the item information.');
        }
    },

    autocomplete: async ({ interaction }) => {
        try {
            const focusedValue = interaction.options.getFocused();
            const filePath = getAllItemsPath();
            const itemsData = JSON.parse(await fs.readFile(filePath, 'utf8'));

            const filtered = itemsData
                .filter(item => item.name.toLowerCase().includes(focusedValue.toLowerCase()))
                .map(item => ({ name: `${item.name} (${item.category})`, value: item.name }));

            await interaction.respond(filtered.slice(0, 25));
        } catch (error) {
            console.error('Error in autocomplete:', error);
            await interaction.respond([]);
        }
    },
 
    options: {
        deleted: false,
    },
};