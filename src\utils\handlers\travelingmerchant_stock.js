const { EmbedBuilder } = require('discord.js');
const { sendWebhookMessage } = require('../webhookManager');
const path = require('path');
const fs = require('fs');

// Helper function to get allItems.json path
const DEFAULT_ALL_ITEMS_PATH = process.env.ALL_ITEMS_PATH || path.join(__dirname, '../../data/allItems.json');
const FALLBACK_ALL_ITEMS_PATH = path.join(__dirname, '../../allItemsAlterable.json');

function getAllItemsPath() {
    try {
        fs.accessSync(DEFAULT_ALL_ITEMS_PATH);
        return DEFAULT_ALL_ITEMS_PATH;
    } catch {
        return FALLBACK_ALL_ITEMS_PATH;
    }
}

// Helper function to add emoji data to items
function addEmojiData(stockData) {
    try {
        const allItemsPath = getAllItemsPath();
        const allItems = JSON.parse(fs.readFileSync(allItemsPath, 'utf8'));

        return stockData.map(item => {
            const itemData = allItems.find(allItem => allItem.name === item.name);
            return {
                ...item,
                emoji: itemData?.emoji || '❓' // Fallback emoji for merchant items
            };
        });
    } catch (error) {
        console.warn('⚠️ Failed to load emoji data:', error.message);
        return stockData.map(item => ({ ...item, emoji: '💰' }));
    }
}

module.exports = async function handleTravelingMerchantStock({ data, config, roleConfig, autoReactionConfig }) {
    // Use webhook for sending
    const webhookUrl = config.merchantWebhookUrl;
    if (!webhookUrl) return;

    // Add emoji data to stock items
    const stockWithEmojis = addEmojiData(data.stock);

    const embed = new EmbedBuilder()
        .setTitle('💰 Merchant Stock Update')
        .setColor('#f1fbd2')
        .setTimestamp()
        .setDescription(stockWithEmojis.map(item => `${item.emoji} **${item.Stock || 0}x** ${item.name}`).join('\n'));

    // Determine merchant type from first item's category
    let content = '';
    if (roleConfig?.merchantRoles && data.stock && data.stock.length > 0) {
        try {
            const allItemsPath = getAllItemsPath();
            const allItems = JSON.parse(fs.readFileSync(allItemsPath, 'utf8'));

            // Find the first item to determine merchant type
            const firstItem = allItems.find(item => item.name === data.stock[0].name);
            if (firstItem && firstItem.category && firstItem.category.includes('Merchant')) {
                const merchantType = firstItem.category;
                const roleId = roleConfig.merchantRoles.get(merchantType);
                if (roleId) {
                    content = `<@&${roleId}>`;
                }
            }
        } catch (error) {
            console.warn('⚠️ Failed to determine merchant type:', error.message);
        }
    }

    // Prepare message data
    const messageData = {
        content,
        embeds: [embed]
    };

    try {
        // Send via webhook
        const webhookMessage = await sendWebhookMessage(webhookUrl, messageData);
        if (webhookMessage === null) {
            console.warn(`⚠️ Failed to send webhook message for guild ${config.guildId}, webhook may be invalid`);
            return;
        }

        //if (autoReactionConfig?.merchantAutoReaction) {
        //    webhookMessage.react('👍');
        //    webhookMessage.react('👎');
        //}
    } catch (error) {
        console.warn(`⚠️ Failed to send merchant stock notification in guild ${config.guildId}: ${error.message}`);
    }
};