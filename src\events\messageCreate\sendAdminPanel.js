const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder } = require("discord.js")

module.exports = async (message, c, client, handler) => {
    if (message.author.id !== '1362805223139049652') return;
    if (message.channel.id !== '1387469626182602904') return;

    const adminCommandsChannel = message.channel;

    const adminCommandEmbed = new EmbedBuilder()
        .setTitle("Admin Commands")
        .setColor("#f1fbd2")
        .setDescription("Use the buttons below to trigger admin commands.")

    const notificationConfigBtn = new ButtonBuilder()
        .setLabel("Toggle Notifications")
        .setCustomId("toggle-stock-updates")
        .setStyle(ButtonStyle.Primary)

    const globalMessageBtn = new ButtonBuilder()
        .setLabel("Send Global Message")
        .setCustomId("global-message-btn")
        .setStyle(ButtonStyle.Secondary)

    const backupApiBtn = new ButtonBuilder()
        .setLabel("Toggle Backup API")
        .setCustomId("toggle-backup-api")
        .setStyle(ButtonStyle.Danger)

    const row = new ActionRowBuilder().addComponents(notificationConfigBtn, globalMessageBtn, backupApiBtn);

    await adminCommandsChannel.send({
        embeds: [adminCommandEmbed],
        components: [row]
    })
};