{"name": "goofbot", "version": "1.0.0", "main": "src/shard.js", "dependencies": {"@prisma/client": "^6.11.1", "ansi-regex": "^6.1.0", "ansi-styles": "^6.2.1", "any-promise": "^1.3.0", "anymatch": "^3.1.3", "array-union": "^2.1.0", "axios": "^1.10.0", "balanced-match": "^1.0.2", "base64-js": "^1.5.1", "binary-extensions": "^2.3.0", "biskviit": "^1.0.1", "bl": "^5.1.0", "bottleneck": "^2.19.5", "brace-expansion": "^2.0.1", "braces": "^3.0.3", "buffer": "^6.0.3", "bundle-require": "^4.2.1", "cac": "^6.7.14", "chalk": "^5.3.0", "chokidar": "^3.6.0", "cli-cursor": "^4.0.0", "cli-spinners": "^2.9.2", "color-convert": "^2.0.1", "color-name": "^1.1.4", "commander": "^11.1.0", "commandkit": "^0.1.10", "cross-spawn": "^7.0.6", "debug": "^4.4.0", "dir-glob": "^3.0.1", "discord-api-types": "^0.37.119", "discord.js": "^14.18.0", "dotenv": "^16.4.7", "eastasianwidth": "^0.2.0", "emoji-regex": "^10.4.0", "encoding": "^0.1.12", "esbuild": "^0.18.20", "execa": "^5.1.1", "fast-deep-equal": "^3.1.3", "fast-glob": "^3.3.2", "fastq": "^1.17.1", "fetch": "^1.1.0", "fill-range": "^7.1.1", "foreground-child": "^3.3.0", "get-stream": "^6.0.1", "glob": "^10.4.5", "glob-parent": "^5.1.2", "globby": "^11.1.0", "human-signals": "^2.1.0", "iconv-lite": "^0.4.24", "ieee754": "^1.2.1", "ignore": "^5.3.2", "inherits": "^2.0.4", "is-binary-path": "^2.1.0", "is-extglob": "^2.1.1", "is-fullwidth-code-point": "^3.0.0", "is-glob": "^4.0.3", "is-interactive": "^2.0.0", "is-number": "^7.0.0", "is-stream": "^2.0.1", "is-unicode-supported": "^1.3.0", "isexe": "^2.0.0", "jackspeak": "^3.4.3", "joycon": "^3.1.1", "lilconfig": "^3.1.3", "lines-and-columns": "^1.2.4", "load-tsconfig": "^0.2.5", "lodash": "^4.17.21", "lodash.snakecase": "^4.1.1", "lodash.sortby": "^4.7.0", "log-symbols": "^5.1.0", "lru-cache": "^10.4.3", "magic-bytes.js": "^1.10.0", "merge-stream": "^2.0.0", "merge2": "^1.4.1", "micromatch": "^4.0.8", "mimic-fn": "^2.1.0", "minimatch": "^9.0.5", "minipass": "^7.1.2", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "ms": "^2.1.3", "mz": "^2.7.0", "normalize-path": "^3.0.0", "npm-run-path": "^4.0.1", "object-assign": "^4.1.1", "onetime": "^5.1.2", "ora": "^7.0.1", "package-json-from-dist": "^1.0.1", "path-key": "^3.1.1", "path-scurry": "^1.11.1", "path-type": "^4.0.0", "picomatch": "^2.3.1", "pirates": "^4.0.6", "postcss-load-config": "^4.0.2", "psl": "^1.15.0", "punycode": "^2.3.1", "queue-microtask": "^1.2.3", "readable-stream": "^3.6.2", "readdirp": "^3.6.0", "resolve-from": "^5.0.0", "restore-cursor": "^4.0.0", "reusify": "^1.0.4", "rfdc": "^1.4.1", "rimraf": "^5.0.10", "rollup": "^3.29.5", "run-parallel": "^1.2.0", "safe-buffer": "^5.2.1", "safer-buffer": "^2.1.2", "shebang-command": "^2.0.0", "shebang-regex": "^3.0.0", "signal-exit": "^3.0.7", "slash": "^3.0.0", "source-map": "^0.8.0-beta.0", "stdin-discarder": "^0.1.0", "string_decoder": "^1.3.0", "string-width": "^6.1.0", "string-width-cjs": "^4.2.3", "strip-ansi": "^7.1.0", "strip-ansi-cjs": "^6.0.1", "strip-final-newline": "^2.0.0", "sucrase": "^3.35.0", "thenify": "^3.3.1", "thenify-all": "^1.6.0", "to-regex-range": "^5.0.1", "topgg-autoposter": "^2.0.2", "tr46": "^1.0.1", "tree-kill": "^1.2.2", "ts-interface-checker": "^0.1.13", "ts-mixer": "^6.0.4", "tslib": "^2.8.1", "tsup": "^7.2.0", "undici": "^6.21.1", "undici-types": "^6.20.0", "util-deprecate": "^1.0.2", "webidl-conversions": "^4.0.2", "whatwg-url": "^7.1.0", "which": "^2.0.2", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "^7.0.0", "ws": "^8.18.1", "yaml": "^2.6.1"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"prisma": "^6.11.1"}}