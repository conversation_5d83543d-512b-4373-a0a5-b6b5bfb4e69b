const { ApplicationCommandOptionType, EmbedBuilder } = require('discord.js');
const os = require('os');

module.exports = {
    data: {
        name: 'stats',
        description: 'View the bot\'s stats',
        options: [
            {
                name: 'hidden',
                description: 'Hide the message?',
                type: ApplicationCommandOptionType.Boolean
            }
        ]
    },

    run: async ({ interaction, client }) => {
        const hidden = interaction.options.getBoolean('hidden') ?? false;

        const results = await client.shard.broadcastEval(() => {
            const guilds = [...this.guilds.cache.values()];
            const users = [...this.users.cache.values()];
            const channels = [...this.channels.cache.values()];

            const memberCount = guilds.reduce((acc, g) => acc + (g.memberCount ?? 0), 0);
            const botCount = users.filter(u => u.bot).length;

            return {
                guildCount: guilds.length,
                memberCount,
                botCount,
                channelCount: channels.length,
                textChannels: channels.filter(c => c.type === 0).length,
                voiceChannels: channels.filter(c => c.type === 2).length,
                categoryChannels: channels.filter(c => c.type === 4).length
            };
        });

        const totals = results.reduce((acc, shard) => {
            for (const [key, value] of Object.entries(shard)) {
                acc[key] = (acc[key] || 0) + value;
            }
            return acc;
        }, {});

        const totalGuilds = totals.guildCount;
        const totalUsers = totals.memberCount;
        const botCount = totals.botCount;
        const humanCount = totalUsers - botCount;
        const textChannels = totals.textChannels;
        const voiceChannels = totals.voiceChannels;
        const categoryChannels = totals.categoryChannels;


        const uptime = process.uptime();
        const ms = Math.round(client.ws.ping);

        // Fetch commands from the API to ensure accuracy
        const commands = await client.application.commands.fetch();
        const commandCount = commands.size;

        const embed = new EmbedBuilder()
            .setAuthor({ name: 'Garden Notifier', iconURL: client.user.displayAvatarURL() })
            .setDescription(`Garden Notifier is used by ${totalGuilds.toLocaleString()} servers with a total member count of ${totalUsers.toLocaleString()}.`)
            .addFields(
                {
                    name: 'Members',
                    value: `> **Total:** ${totalUsers.toLocaleString()}\n> **Human:** ${humanCount.toLocaleString()}\n> **Bots:** ${botCount.toLocaleString()}`,
                    inline: true
                },
                {
                    name: 'Channels',
                    value: `> **Text:** ${textChannels.toLocaleString()}\n> **Voice:** ${voiceChannels.toLocaleString()}\n> **Categories:** ${categoryChannels.toLocaleString()}`,
                    inline: true
                },
                {
                    name: 'System',
                    value: `> **Commands:** ${commandCount}\n> **Discord.js:** v${require('discord.js').version}\n> **Uptime:** ${Math.floor(uptime / 60)}m`,
                    inline: true
                }
            )
            .setFooter({ text: `${ms}ms • discord.gg/K6FArFC25k` })
            .setColor('#f1fbd2');

        await interaction.reply({
            embeds: [embed],
            ephemeral: hidden
        });
    },

    options: {
        deleted: false,
    },
};