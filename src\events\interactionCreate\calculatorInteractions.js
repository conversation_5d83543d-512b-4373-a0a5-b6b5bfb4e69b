const { EmbedBuilder, MessageFlags } = require('discord.js');

function formatPrice(value) {
    const num = Number(value);
    let short = '';
    if (num >= 1_000_000_000_000_000_000) {
        short = (num / 1_000_000_000_000_000_000).toFixed(1).replace(/\.0$/, '') + 'Qi';
    } else if (num >= 1_000_000_000_000_000) {
        short = (num / 1_000_000_000_000_000).toFixed(1).replace(/\.0$/, '') + 'Qa';
    } else if (num >= 1_000_000_000_000) {
        short = (num / 1_000_000_000_000).toFixed(1).replace(/\.0$/, '') + 'T';
    } else if (num >= 1_000_000_000) {
        short = (num / 1_000_000_000).toFixed(1).replace(/\.0$/, '') + 'B';
    } else if (num >= 1_000_000) {
        short = (num / 1_000_000).toFixed(1).replace(/\.0$/, '') + 'M';
    } else if (num >= 1_000) {
        short = (num / 1_000).toFixed(1).replace(/\.0$/, '') + 'K';
    } else {
        short = num.toString();
    }
    return num.toLocaleString() + ' (' + short + ')';
}

module.exports = async (interaction, c, client, handler) => {
    if (!interaction.customId) return;

    let type, index, userId;

    const parts = interaction.customId.split('-');

    if (interaction.customId.startsWith('mutation')) {
        [type, index, userId] = parts; // e.g. mutation-select-1-userId
    } else if (interaction.customId.startsWith('variant')) {
        [type, userId] = parts; // e.g. variant-select-userId
    } else {
        return; // Not one of our menus
    }

    if (interaction.user.id !== userId) {
        return interaction.reply({
            content: `You do not own this calculator. Create your own by running "/calculator".`,
            flags: MessageFlags.Ephemeral
        });
    }

    const message = interaction.message;
    const embed = message.embeds[0];

    const lines = embed.description.split('\n');
    const fruit = lines.find(l => l.startsWith('**Fruit:**'))?.split('**')[2]?.trim();
    const weight = parseFloat(lines.find(l => l.startsWith('**Weight:**'))?.split('**')[2]);

    // Initialize stored selections
    if (!message.variants) message.variants = [];
    if (!message.mutations1) message.mutations1 = [];
    if (!message.mutations2) message.mutations2 = [];

    if (type === 'variant') {
        message.variants = interaction.values;
    }

    if (type === 'mutation') {
        if (index === '1') {
            message.mutations1 = interaction.values;
        } else if (index === '2') {
            message.mutations2 = interaction.values;
        }
    }

    const allMutations = [...(message.mutations1 || []), ...(message.mutations2 || [])];
    const selectedVariants = message.variants?.join(', ') || 'None';
    const selectedMutations = allMutations.length > 0 ? allMutations.join(', ') : 'None';

    try {
        const url = `https://alpha-v0-lama.3itx.tech/api/v1/calculator?Name=${encodeURIComponent(fruit)}&Variant=${encodeURIComponent(selectedVariants)}&Weight=${weight}&Mutation=${encodeURIComponent(selectedMutations)}`;
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'x-api-key': process.env.API_TOKEN
            },
        });

        if (!response.ok) {
            throw new Error(`API request failed with status ${response.status}`);
        }

        const data = await response.json();

        const formatedPrice = formatPrice(data.value);

        const updatedEmbed = EmbedBuilder.from(embed).setDescription(
            `Calculator for **${fruit}**\n\n**Fruit:** ${fruit}\n**Weight:** ${weight}kg\n**Variant:** ${selectedVariants}\n**Mutations:** ${selectedMutations}\n\n**Sell Price:** ${formatedPrice} sheckles\n\n-# Use the select menus below to add mutations and variants to your fruit.`
        );

        await interaction.update({
            embeds: [updatedEmbed],
            components: message.components
        });

    } catch (error) {
        console.error('Error in calculator interaction:', error);
        return interaction.reply({
            content: 'An error occurred while updating the calculator. Please try again later.',
            flags: MessageFlags.Ephemeral
        });
    }
};