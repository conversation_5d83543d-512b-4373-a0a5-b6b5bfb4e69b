const prisma = require('../../lib/prisma');

class AutoReactionConfig {
    /**
     * Find an auto reaction config by guild ID
     * @param {string} guildId - The guild ID
     * @returns {Promise<Object|null>} The auto reaction config or null
     */
    static async findOne(query) {
        if (query.guildId) {
            return await prisma.autoReactionConfig.findUnique({
                where: { guildId: query.guildId }
            });
        }
        return null;
    }

    /**
     * Find all auto reaction configs
     * @returns {Promise<Array>} Array of auto reaction configs
     */
    static async find(query = {}) {
        const where = {};
        if (query.guildId) {
            where.guildId = query.guildId;
        }
        
        return await prisma.autoReactionConfig.findMany({
            where
        });
    }

    /**
     * Create or update an auto reaction config
     * @param {Object} filter - Filter criteria
     * @param {Object} update - Update data
     * @param {Object} options - Options (upsert, etc.)
     * @returns {Promise<Object>} The created/updated auto reaction config
     */
    static async findOneAndUpdate(filter, update, options = {}) {
        const { guildId } = filter;
        const updateData = { ...update };
        delete updateData.guildId; // Remove guildId from update data
        
        if (options.upsert) {
            return await prisma.autoReactionConfig.upsert({
                where: { guildId },
                update: updateData,
                create: { guildId, ...updateData }
            });
        } else {
            return await prisma.autoReactionConfig.update({
                where: { guildId },
                data: updateData
            });
        }
    }

    /**
     * Create a new auto reaction config
     * @param {Object} data - The auto reaction config data
     * @returns {Promise<Object>} The created auto reaction config
     */
    static async create(data) {
        return await prisma.autoReactionConfig.create({
            data
        });
    }

    /**
     * Delete an auto reaction config
     * @param {Object} filter - Filter criteria
     * @returns {Promise<Object|null>} The deleted auto reaction config or null if not found
     */
    static async findOneAndDelete(filter) {
        const { guildId } = filter;
        try {
            return await prisma.autoReactionConfig.delete({
                where: { guildId }
            });
        } catch (error) {
            // If record doesn't exist, return null (like Mongoose behavior)
            if (error.code === 'P2025') {
                return null;
            }
            throw error;
        }
    }
}

module.exports = AutoReactionConfig;
