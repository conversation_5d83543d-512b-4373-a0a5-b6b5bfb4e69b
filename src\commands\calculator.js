const {
    ActionRowBuilder,
    EmbedBuilder,
    MessageFlags,
    ApplicationCommandOptionType,
    StringSelectMenuOptionBuilder,
    StringSelectMenuBuilder
} = require('discord.js');
const fs = require('fs/promises');
const path = require('path');

function formatPrice(value) {
    const num = Number(value);
    let short = '';
    if (num >= 1_000_000_000_000_000_000) {
        short = (num / 1_000_000_000_000_000_000).toFixed(1).replace(/\.0$/, '') + 'Qi';
    } else if (num >= 1_000_000_000_000_000) {
        short = (num / 1_000_000_000_000_000).toFixed(1).replace(/\.0$/, '') + 'Qa';
    } else if (num >= 1_000_000_000_000) {
        short = (num / 1_000_000_000_000).toFixed(1).replace(/\.0$/, '') + 'T';
    } else if (num >= 1_000_000_000) {
        short = (num / 1_000_000_000).toFixed(1).replace(/\.0$/, '') + 'B';
    } else if (num >= 1_000_000) {
        short = (num / 1_000_000).toFixed(1).replace(/\.0$/, '') + 'M';
    } else if (num >= 1_000) {
        short = (num / 1_000).toFixed(1).replace(/\.0$/, '') + 'K';
    } else {
        short = num.toString();
    }
    return num.toLocaleString() + ' (' + short + ')';
}

module.exports = {
    data: {
        name: 'calculator',
        description: 'Calculate the price of a fruit.',
        options: [
            {
                name: 'fruit',
                description: 'The fruit to calculate the price for',
                type: ApplicationCommandOptionType.String,
                required: true,
                autocomplete: true
            },
            {
                name: 'weight',
                description: 'The weight of the fruit',
                type: ApplicationCommandOptionType.Number,
                required: true,
            }
        ]
    },

    run: async ({ interaction, client, handler }) => {
        await interaction.deferReply();

        try {
            const fruitName = interaction.options.getString('fruit');
            const weight = interaction.options.getNumber('weight');

            const response = await fetch(`https://alpha-v0-lama.3itx.tech/api/v1/calculator?Name=${encodeURIComponent(fruitName)}&Weight=${weight}`, {
                method: 'GET',
                headers: {
                    'x-api-key': process.env.API_TOKEN
                },
            });

            const data = await response.json();

            const formatedPrice = formatPrice(data.value);

            const embed = new EmbedBuilder()
                .setTitle('Fruit Calculator')
                .setDescription(`Calculator for **${fruitName}**\n\n**Fruit:** ${fruitName}\n**Weight:** ${weight}kg\n**Variant:** None\n**Mutations:** None\n\n**Sell Price:** ${formatedPrice} sheckles\n\n-# Use the select menus below to add mutations and variants to your fruit.`)
                .setColor('#f1fbd2');

            const variantSelect = new StringSelectMenuBuilder()
                .setCustomId(`variant-${interaction.user.id}`)
                .setPlaceholder('Select a variant')
                .setMaxValues(1)
                .addOptions(
                    new StringSelectMenuOptionBuilder().setLabel('Normal').setValue('Normal'),
                    new StringSelectMenuOptionBuilder().setLabel('Gold').setValue('Gold'),
                    new StringSelectMenuOptionBuilder().setLabel('Rainbow').setValue('Rainbow')
                );

            // All mutations (41 items)
            const allMutations = [
                'Meteoric', 'Celestial', 'Windstruck', 'Fried', 'Burnt', 'Drenched', 'Tempestuous', 'Shocked',
                'Aurora', 'Choc', 'Bloodlit', 'Friendbound', 'Paradisal', 'Clay', 'Amber', 'Zombified',
                'HoneyGlazed', 'Frozen', 'OldAmber', 'Verdant', 'Sandy', 'Moonlit', 'Infected', 'AncientAmber',
                'Ceramic', 'Pollinated', 'Disco', 'Twisted', 'Alienlike', 'Plasma', 'Molten', 'Dawnbound',
                'Cooked', 'Chilled', 'Heavenly', 'Wiltproof', 'Sundried', 'Galactic', 'Voidtouched', 'Wet',
                'Cloudtouched'
            ];

            const half = Math.ceil(allMutations.length / 2);
            const mutations1 = allMutations.slice(0, half);
            const mutations2 = allMutations.slice(half);

            const mutationSelect1 = new StringSelectMenuBuilder()
                .setCustomId(`mutation-1-${interaction.user.id}`)
                .setPlaceholder('Select mutations (1)')
                .setMaxValues(mutations1.length)
                .addOptions(mutations1.map(name =>
                    new StringSelectMenuOptionBuilder().setLabel(name).setValue(name)
                ));

            const mutationSelect2 = new StringSelectMenuBuilder()
                .setCustomId(`mutation-2-${interaction.user.id}`)
                .setPlaceholder('Select mutations (2)')
                .setMaxValues(mutations2.length)
                .addOptions(mutations2.map(name =>
                    new StringSelectMenuOptionBuilder().setLabel(name).setValue(name)
                ));

            const actionRowOne = new ActionRowBuilder().addComponents(variantSelect);
            const actionRowTwo = new ActionRowBuilder().addComponents(mutationSelect1);
            const actionRowThree = new ActionRowBuilder().addComponents(mutationSelect2);

            await interaction.editReply({
                embeds: [embed],
                components: [actionRowOne, actionRowTwo, actionRowThree]
            });

        } catch (error) {
            console.error('Error in calculate command:', error);
            await interaction.editReply({
                content: 'An error occurred while creating a calculator instance. Please try again later. If the issue persists, join the support server and report the issue.',
                flags: MessageFlags.Ephemeral
            });
        }
    },

    autocomplete: async ({ interaction }) => {
        try {
            const response = await fetch('https://alpha-v0-lama.3itx.tech/api/v1/info?Type=Crop', {
                method: 'GET',
                headers: {
                    'x-api-key': process.env.API_TOKEN
                },
            });

            const itemsData = await response.json();
            const focusedValue = interaction.options.getFocused();

            const filtered = itemsData.data
                .filter(item => item.Name.toLowerCase().includes(focusedValue.toLowerCase()))
                .map(item => ({ name: `${item.Name} (${item.Type})`, value: item.Name }));

            await interaction.respond(filtered.slice(0, 25));
        } catch (error) {
            console.error('Error in autocomplete:', error);
            await interaction.respond([]);
        }
    },

    options: {
        deleted: false,
    }
};