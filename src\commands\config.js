const {
    SlashCommandBuilder,
    PermissionFlagsBits,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    EmbedBuilder,
    MessageFlags
} = require('discord.js');
const StockNotification = require('../models/StockNotification');
const SeedRoleConfig = require('../models/roleConfig');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('config')
        .setDescription('Configure stock notification settings')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild),

    run: async ({ interaction, client, handler }) => {
        await interaction.deferReply({ flags: MessageFlags.Ephemeral });

        try {
            const notification = await StockNotification.findOne({ guildId: interaction.guildId });
            const roleConfig = await SeedRoleConfig.findOne({ guildId: interaction.guildId });

            const mainEmbed = new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Stock Notification Configuration')
                .setDescription('Configure webhook-based stock notifications and role pings for your server.\n\n**Note:** Requires `Manage Webhooks` permission in target channels.')
                .addFields([
                    {
                        name: '📢 Seeds & Gears Notifications',
                        value: notification && notification.webhookUrl ? '✅ Webhook configured' : '❌ Not configured',
                        inline: true
                    },
                    {
                        name: '🥚 Egg Notifications',
                        value: notification && notification.eggWebhookUrl ? '✅ Webhook configured' : '❌ Not configured',
                        inline: true
                    },
                    {
                        name: '🌦️ Weather Notifications',
                        value: notification && notification.weatherWebhookUrl ? '✅ Webhook configured' : '❌ Not configured',
                        inline: true
                    },
                    {
                        name: '🎉 Event Shop Notifications',
                        value: notification && notification.eventWebhookUrl ? '✅ Webhook configured' : '❌ Not configured',
                        inline: true
                    },
                    {
                        name: '🎁 Cosmetic Notifications',
                        value: notification && notification.cosmeticWebhookUrl ? '✅ Webhook configured' : '❌ Not configured',
                        inline: true
                    },
                    {
                        name: '💰 Merchant Notifications',
                        value: notification && notification.merchantWebhookUrl ? '✅ Webhook configured' : '❌ Not configured',
                        inline: true
                    },
                    {
                        name: '🔔 Seed Role Pings',
                        value: roleConfig ? `${roleConfig.seedRoles.size} roles configured` : 'Not configured',
                        inline: true
                    },
                    {
                        name: '🛠️ Gear Role Pings',
                        value: roleConfig ? `${roleConfig.gearRoles?.size || 0} roles configured` : 'Not configured',
                        inline: true
                    },
                    {
                        name: '🥚 Egg Role Pings',
                        value: roleConfig ? `${roleConfig.eggRoles?.size || 0} roles configured` : 'Not configured',
                        inline: true
                    },
                    {
                        name: '🌤️ Weather Role Pings',
                        value: roleConfig ? `${roleConfig.weatherRoles?.size || 0} roles configured` : 'Not configured',
                        inline: true
                    },
                    {
                        name: '🎉 Event Role Pings',
                        value: roleConfig ? `${roleConfig.eventRoles?.size || 0} roles configured` : 'Not configured',
                        inline: true
                    },
                    {
                        name: '🎁 Cosmetic Role Ping',
                        value: roleConfig && roleConfig.cosmeticRole ? `<@&${roleConfig.cosmeticRole}>` : 'Not configured',
                        inline: true
                    },
                    {
                        name: '💰 Merchant Role Pings',
                        value: roleConfig && roleConfig.merchantRoles ? `${roleConfig.merchantRoles.size || 0} roles configured` : 'Not configured',
                        inline: true
                    }
                ]);

            const seedsGearButton = new ButtonBuilder()
                .setCustomId('seedsGearNavBtn')
                .setLabel('Seeds/Gear')
                .setStyle(ButtonStyle.Primary);

            const eggsButton = new ButtonBuilder()
                .setCustomId('eggsNavBtn')
                .setLabel('Eggs')
                .setStyle(ButtonStyle.Primary);

            const weatherButton = new ButtonBuilder()
                .setCustomId('weatherNavBtn')
                .setLabel('Weather')
                .setStyle(ButtonStyle.Primary);

            const eventButton = new ButtonBuilder()
                .setCustomId('eventNavBtn')
                .setLabel('Event Shop')
                .setStyle(ButtonStyle.Primary);

            const cosmeticButton = new ButtonBuilder()
                .setCustomId('cosmeticNavBtn')
                .setLabel('Cosmetics')
                .setStyle(ButtonStyle.Primary);

            const merchantButton = new ButtonBuilder()
                .setCustomId('merchantNavBtn')
                .setLabel('Merchant')
                .setStyle(ButtonStyle.Primary);

            const removeButton = new ButtonBuilder()
                .setCustomId('removeConfigBtn')
                .setLabel('Remove All Settings')
                .setStyle(ButtonStyle.Danger);

            const row = new ActionRowBuilder().addComponents(seedsGearButton, eggsButton, weatherButton);
            const row2 = new ActionRowBuilder().addComponents(eventButton, cosmeticButton, merchantButton);
            const row3 = new ActionRowBuilder().addComponents(removeButton);

            await interaction.editReply({
                embeds: [mainEmbed],
                components: [row, row2, row3]
            });
        } catch (error) {
            console.error('Error in config command:', error);
            await interaction.editReply({
                content: 'An error occurred while managing the configuration.',
                flags: MessageFlags.Ephemeral
            });
        }
    },
};