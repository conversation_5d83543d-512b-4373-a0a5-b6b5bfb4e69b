module.exports = async (guild, client) => {
    const memberCount = guild.memberCount || (await guild.fetch()).memberCount;

    await client.shard.broadcastEval(async (c, { guildName, guildId, memberCount }) => {
        const channelId = '1386069613472776453';
        const channel = await c.channels.fetch(channelId).catch(() => null);
        if (!channel) return;

        channel.send(
            `<:add:1386074651830259752> Added to **${guildName}** (${guildId}) with ${memberCount} members`
        );
    }, {
        context: {
            guildName: guild.name,
            guildId: guild.id,
            memberCount
        }
    });
};