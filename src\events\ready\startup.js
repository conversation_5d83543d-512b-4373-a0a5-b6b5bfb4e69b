require('dotenv').config();

const { ActivityType } = require('discord.js');
const GardenSocket = require('../../utils/stockUpdateHandler');

module.exports = async (c, client, handler) => {
    console.log(`Logged in as ${client.user.tag} on shard ${client.shard?.ids?.[0]}`);

    client.user.setPresence({ 
        activities: [{
            type: ActivityType.Watching,
            name: `seed stocks | Shard ${client.shard?.ids?.[0]}`,
            status: 'online',
        }],
    });

    const wsUrl = 'wss://ws.3itx.tech';
    const authToken = process.env.API_TOKEN;

    if (client.shard.ids.includes(0)) {
        const socket = new GardenSocket(wsUrl, authToken, client);
        socket.connect();
    }
};