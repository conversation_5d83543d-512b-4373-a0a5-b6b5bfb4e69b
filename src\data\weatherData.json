[{"name": "Alien Invasion", "apiName": "AlienInvasion", "type": "admin", "lastSeen": "", "effects": ["Has a chance to apply a Alienlike mutation", "Skybox changes to a light-blue, aliens spawn in the skybox"]}, {"name": "Aurora Borealis", "apiName": "AuroraBorealis", "type": "standard", "lastSeen": "2025-07-02T18:24:07.996Z", "effects": ["Gives the Aurora mutation"]}, {"name": "<PERSON><PERSON>", "apiName": "<PERSON><PERSON>", "type": "admin", "lastSeen": "", "effects": ["Gives plants the Pollinated mutation"]}, {"name": "Black Hole", "apiName": "BlackHole", "type": "admin", "lastSeen": "", "effects": ["Gives the plants Void Touched mutation", "Plants with this mutation have purple void portals as particles"]}, {"name": "Blood Moon", "apiName": "BloodMoonEvent", "type": "standard", "lastSeen": "", "effects": ["Gives crops a glowing red hue", "Chance to apply Bloodlit"]}, {"name": "Chicken Rain", "apiName": "ChickenRain", "type": "admin", "lastSeen": "", "effects": ["Has a chance to apply the Fried mutation"]}, {"name": "Chocolate Rain", "apiName": "ChocolateRain", "type": "admin", "lastSeen": "", "effects": ["Functions similarly to standard Rain", "Replaces the Wet mutation with the Chocolate mutation for crops"]}, {"name": "Disco", "apiName": "Disco", "type": "admin", "lastSeen": "", "effects": ["Turns the screen rainbow-colored", "Every second, fruits/crops have a chance to receive the Disco mutation", "Forces characters to dance with 1 of 3 default Roblox dances"]}, {"name": "DJ <PERSON><PERSON>", "apiName": "<PERSON><PERSON><PERSON>", "type": "admin", "lastSeen": "", "effects": ["None"]}, {"name": "DJ Sam", "apiName": "DJSam", "type": "admin", "lastSeen": "", "effects": ["None"]}, {"name": "Solar Flare", "apiName": "SolarFlare", "type": "admin", "lastSeen": "", "effects": ["The solar flare will apply Sundried such as Verdant to some fruit."]}, {"name": "Fried Chicken", "apiName": "FriedChicken", "type": "admin", "lastSeen": "", "effects": ["Has a chance to apply the Fried mutation."]}, {"name": "Pool Party", "apiName": "PoolParty", "type": "admin", "lastSeen": "", "effects": ["Everyone gets a free Duckfloat cosmetic."]}, {"name": "<PERSON>s", "apiName": "<PERSON>s", "type": "admin", "lastSeen": "", "effects": ["Increased Infected Mutation Chance.", "Multiple Zombies are spawned around the map. If a player touches them, the player's arms will be extended outwards like a zombie. The player will then be able to infect other players by touching them."]}, {"name": "<PERSON>", "apiName": "CrystalBeams", "type": "admin", "lastSeen": "", "effects": ["Once all crystals have been activated, plants have a chance to get a few mutations."]}, {"name": "Text Collect", "apiName": "TextCollect", "type": "admin", "lastSeen": "", "effects": ["Once all letters have been collected, plants have a chance to turn Disco."]}, {"name": "<PERSON><PERSON>", "apiName": "<PERSON><PERSON>", "type": "admin", "lastSeen": "", "effects": ["Obby appears, if the player passes the obby, they will receive a special jump pad cosmetic which gives a jump boost when touched."]}, {"name": "<PERSON>", "apiName": "<PERSON>", "type": "standard", "lastSeen": "", "effects": ["Increases growth speed by 50%", "Chance to apply Chilled", "Combines with Wet to create Frozen", "Triggers shivering animation for players, along with a shivering sound effect"]}, {"name": "Gale", "apiName": "Gale", "type": "standard", "lastSeen": "", "effects": ["Crops have a higher chance to become Windstruck during the event", "Players will be blown by strong wind currents"]}, {"name": "Heatwave", "apiName": "Heatwave", "type": "event", "lastSeen": "", "effects": ["Applies the Sundried mutation to crops"]}, {"name": "Floating <PERSON><PERSON>", "apiName": "JandelFloat", "type": "admin", "lastSeen": "", "effects": ["Make crops have a chance to get Heavenly mutation"]}, {"name": "<PERSON><PERSON>", "apiName": "JandelStorm", "type": "admin", "lastSeen": "", "effects": ["Spawns lightning strikes at 4 strikes/second", "A gigantic Jandel entity appears at the start, which can be seen behind the gears, cosmetics, and eggs shop"]}, {"name": "Lazer Storm", "apiName": "LazerStorm", "type": "admin", "lastSeen": "", "effects": ["Gives crops the Plasma mutation"]}, {"name": "Luck", "apiName": "Luck", "type": "admin", "lastSeen": "", "effects": ["Currently, Luck Event is a visual, and its feature is capped by developers"]}, {"name": "Meteor Shower", "apiName": "MeteorShower", "type": "standard", "lastSeen": "", "effects": ["Meteors fall from the sky", "Crops hit by meteors gain Celestial mutation"]}, {"name": "Monster Mash", "apiName": "MonsterMash", "type": "admin", "lastSeen": "", "effects": ["DJ <PERSON><PERSON> was shown behind the Pet Eggs and Gear Shop with a DJ booth, causing effects and forcing players to dance"]}, {"name": "Night", "apiName": "NightEvent", "type": "standard", "lastSeen": "2025-07-02T16:30:33.250Z", "effects": ["Gives crops a glowing purple hue", "Chance to apply Moonlit", "6 crops become Moonlit per night (once every 40 seconds over 4 minutes)"]}, {"name": "Rain", "apiName": "Rain", "type": "standard", "lastSeen": "2025-06-29T23:04:06.423Z", "effects": ["Increases crop growth speed by 50%", "50% chance to apply the Wet mutation", "Can combine with Chilled to create Frozen"]}, {"name": "Sandstorm", "apiName": "Sandstorm", "type": "standard", "lastSeen": "", "effects": ["Gives the sandy mutation"]}, {"name": "Sheckle <PERSON>", "apiName": "<PERSON><PERSON><PERSON><PERSON>", "type": "admin", "lastSeen": "", "effects": ["Turns the sky yellowish/golden", "Sheckles fall from the sky and can be collected - Avg.: 30 sheckles"]}, {"name": "Space Travel", "apiName": "SpaceTravel", "type": "admin", "lastSeen": "", "effects": ["+75% Growth Speed", "Has a chance to apply a Galactic mutation", "The gravity becomes lower"]}, {"name": "Sun God", "apiName": "SunGod", "type": "event", "lastSeen": "2025-07-02T18:45:13.407Z", "effects": ["Applies the Dawnbound mutation to 4+ sunflowers when presented in front of the Sun God"]}, {"name": "Thunderstorm", "apiName": "Thunderstorm", "type": "standard", "lastSeen": "2025-07-02T15:32:22.328Z", "effects": ["Increases growth speed by 50%", "50% chance to apply Wet", "Lightning strikes can apply Shocked mutation"]}, {"name": "Tornado", "apiName": "Tornado", "type": "standard", "lastSeen": "2025-07-02T19:39:19.928Z", "effects": ["Gives crops the Twisted mutation"]}, {"name": "Tropical Rain", "apiName": "TropicalRain", "type": "standard", "lastSeen": "2025-07-02T18:34:25.891Z", "effects": ["Gives +50% Grow Speed, & crops the Drenched mutation"]}, {"name": "Under The Sea", "apiName": "UnderTheSea", "type": "admin", "lastSeen": "", "effects": ["Has a chance to apply the Wet mutation", "Skybox changes to a light-blue, players can swim on the map"]}, {"name": "Volcano", "apiName": "Volcano", "type": "admin", "lastSeen": "", "effects": ["Has a likelihood to apply a Molten mutation"]}, {"name": "Windy", "apiName": "Windy", "type": "standard", "lastSeen": "2025-07-02T19:44:09.596Z", "effects": ["Gives crops a slightly noticeable crescent wind effect", "Crops have a chance to become Windstruck during the event"]}]