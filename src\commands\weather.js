require('dotenv').config();

const { MessageFlags, EmbedBuilder } = require("discord.js");

module.exports = {
    data: {
        name: 'weather',
        description: 'Check the current weather'
    },
 
    run: async ({ interaction, client, handler }) => {
        await interaction.deferReply();

        try {
            const response = await fetch('https://alpha-v0-lama.3itx.tech/api/v1/weather', {
                method: 'GET',
                headers: {
                    'x-api-key': process.env.API_TOKEN
                },
            });
            const data = await response.json();
            const weatherData = data.weather || [];
            const weatherList = weatherData.map(weather => `- **${weather.weather}**:\n> Duration: ${weather.duration}\n> Started: <t:${weather.start_timestamp_unix}:R>\n> Ending: <t:${weather.end_timestamp_unix}:R>`).join('\n');

            const embed = new EmbedBuilder()
                .setColor('#f1fbd2')
                .setTitle('Current Weather')
                .setDescription(weatherList || 'No weather active')
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });
        } catch (error) {
            await interaction.reply({ content: "Sorry, there was an error fetching the weather data.", flags: MessageFlags.Ephemeral });
        }
    },
 
    options: {
        deleted: false,
    },
};