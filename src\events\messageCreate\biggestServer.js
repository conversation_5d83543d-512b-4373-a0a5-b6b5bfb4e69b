module.exports = async (message, c, client, handler) => {
    if (message.author.id !== '1362805223139049652') return;
    if (message.content.includes("<@1383147672374087710>") && message.content.includes("!biggestServer")) {
        const results = await c.shard.broadcastEval(c => {
            const biggest = c.guilds.cache
            .map(g => ({ id: g.id, name: g.name, memberCount: g.memberCount }))
            .sort((a, b) => b.memberCount - a.memberCount)[0];
            return biggest;
        });

        // Combine results from all shards
        const allResults = results.filter(Boolean); // remove nulls
        const biggestOverall = allResults.sort((a, b) => b.memberCount - a.memberCount)[0];

        if (biggestOverall) {
            message.channel.send(`🏆 Biggest server: **${biggestOverall.name}** (${biggestOverall.id}) with **${biggestOverall.memberCount}** members. Owned by <@${biggestOverall.ownerId}>`);
        } else {
            message.channel.send('Could not find any servers.');
        }
    }
};